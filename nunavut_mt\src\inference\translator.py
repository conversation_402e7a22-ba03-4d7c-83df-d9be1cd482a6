"""
Translation inference module for the bidirectional English-Inuktitut MT model.

This module provides translation functionality with support for both
English-to-Inuktitut and Inuktitut-to-English translation directions.
"""

import torch
import sentencepiece as smp
from pathlib import Path
from typing import List, Dict, Union, Optional
import logging
import json
import time

logger = logging.getLogger(__name__)


class SimpleTranslator:
    """Simple translator using SentencePiece tokenizer for demonstration."""
    
    def __init__(self, tokenizer_path: str, device: str = "cpu"):
        """
        Initialize the translator.
        
        Args:
            tokenizer_path: Path to the trained SentencePiece model
            device: Device to run inference on
        """
        self.device = device
        self.tokenizer_path = tokenizer_path
        
        # Load tokenizer
        self.sp = smp.SentencePieceProcessor()
        self.sp.load(tokenizer_path)
        
        # Special tokens
        self.pad_token_id = 0
        self.unk_token_id = 1
        self.bos_token_id = 2
        self.eos_token_id = 3
        self.en2iu_token = "<en2iu>"
        self.iu2en_token = "<iu2en>"
        self.en2iu_token_id = self.sp.piece_to_id(self.en2iu_token)
        self.iu2en_token_id = self.sp.piece_to_id(self.iu2en_token)
        
        logger.info(f"Translator initialized with tokenizer: {tokenizer_path}")
        logger.info(f"Vocabulary size: {self.sp.get_piece_size()}")
        logger.info(f"Special token IDs: en2iu={self.en2iu_token_id}, iu2en={self.iu2en_token_id}")
    
    def encode_text(self, text: str, direction: str = "en2iu") -> List[int]:
        """
        Encode text with direction token.
        
        Args:
            text: Input text to encode
            direction: Translation direction ("en2iu" or "iu2en")
            
        Returns:
            List of token IDs
        """
        # Add direction token
        direction_token = self.en2iu_token if direction == "en2iu" else self.iu2en_token
        prefixed_text = f"{direction_token} {text}"
        
        # Encode with SentencePiece
        token_ids = self.sp.encode_as_ids(prefixed_text)
        
        # Add BOS token
        return [self.bos_token_id] + token_ids
    
    def decode_tokens(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """
        Decode token IDs to text.
        
        Args:
            token_ids: List of token IDs
            skip_special_tokens: Whether to skip special tokens
            
        Returns:
            Decoded text
        """
        # Remove BOS/EOS tokens if present
        if token_ids and token_ids[0] == self.bos_token_id:
            token_ids = token_ids[1:]
        if token_ids and token_ids[-1] == self.eos_token_id:
            token_ids = token_ids[:-1]
            
        text = self.sp.decode_ids(token_ids)
        
        if skip_special_tokens:
            # Remove direction tokens
            text = text.replace(self.en2iu_token, "").replace(self.iu2en_token, "")
            text = text.strip()
            
        return text
    
    def translate_simple(self, text: str, direction: str = "en2iu") -> str:
        """
        Simple rule-based translation for demonstration.
        
        This is a placeholder that would be replaced with actual model inference.
        
        Args:
            text: Input text to translate
            direction: Translation direction
            
        Returns:
            Translated text
        """
        # Simple demonstration translations
        if direction == "en2iu":
            # English to Inuktitut
            simple_translations = {
                "thank you": "ᖁᔭᓐᓇᒦᒃ",
                "hello": "ᐊᐃ",
                "goodbye": "ᐊᔪᙱᓚᖅ",
                "yes": "ᐄ",
                "no": "ᐊᒃᑲ",
                "mr. speaker": "ᐃᒃᓯᕙᐅᑖᖅ",
                "government": "ᒐᕙᒪᒃᑯᑦ",
                "nunavut": "ᓄᓇᕗᑦ",
                "education": "ᐃᓕᓐᓂᐊᖅᑐᓕᕆᓂᖅ",
                "health": "ᐋᓐᓂᐊᖃᕐᓇᙱᑦᑐᓕᕆᓂᖅ",
                "the": "",  # Often omitted in Inuktitut
                "is": "",   # Often omitted in Inuktitut
                "are": "",  # Often omitted in Inuktitut
            }
            
            # Simple word-by-word replacement
            words = text.lower().split()
            translated_words = []
            
            for word in words:
                # Remove punctuation for lookup
                clean_word = word.strip(".,!?;:")
                if clean_word in simple_translations:
                    translation = simple_translations[clean_word]
                    if translation:  # Don't add empty translations
                        translated_words.append(translation)
                else:
                    # Keep unknown words as-is
                    translated_words.append(word)
            
            return " ".join(translated_words)
        
        else:
            # Inuktitut to English
            simple_translations = {
                "ᖁᔭᓐᓇᒦᒃ": "thank you",
                "ᐊᐃ": "hello",
                "ᐊᔪᙱᓚᖅ": "goodbye",
                "ᐄ": "yes",
                "ᐊᒃᑲ": "no",
                "ᐃᒃᓯᕙᐅᑖᖅ": "Mr. Speaker",
                "ᒐᕙᒪᒃᑯᑦ": "government",
                "ᓄᓇᕗᑦ": "Nunavut",
                "ᐃᓕᓐᓂᐊᖅᑐᓕᕆᓂᖅ": "education",
                "ᐋᓐᓂᐊᖃᕐᓇᙱᑦᑐᓕᕆᓂᖅ": "health",
            }
            
            # Simple word-by-word replacement
            words = text.split()
            translated_words = []
            
            for word in words:
                # Remove punctuation for lookup
                clean_word = word.strip(".,!?;:")
                if clean_word in simple_translations:
                    translated_words.append(simple_translations[clean_word])
                else:
                    # Keep unknown words as-is
                    translated_words.append(word)
            
            return " ".join(translated_words)
    
    def translate(self, text: str, direction: str = "en2iu") -> Dict[str, Union[str, float]]:
        """
        Translate text with timing and metadata.
        
        Args:
            text: Input text to translate
            direction: Translation direction
            
        Returns:
            Dictionary with translation and metadata
        """
        start_time = time.time()
        
        # Encode input
        input_ids = self.encode_text(text, direction)
        
        # Perform translation (using simple rule-based for demo)
        translation = self.translate_simple(text, direction)
        
        # Calculate timing
        inference_time = time.time() - start_time
        
        return {
            'input_text': text,
            'translation': translation,
            'direction': direction,
            'input_tokens': len(input_ids),
            'inference_time': inference_time,
            'tokens_per_second': len(input_ids) / inference_time if inference_time > 0 else 0
        }
    
    def translate_batch(self, texts: List[str], direction: str = "en2iu") -> List[Dict[str, Union[str, float]]]:
        """
        Translate a batch of texts.
        
        Args:
            texts: List of input texts
            direction: Translation direction
            
        Returns:
            List of translation results
        """
        results = []
        start_time = time.time()
        
        for text in texts:
            result = self.translate(text, direction)
            results.append(result)
        
        total_time = time.time() - start_time
        
        # Add batch statistics
        batch_stats = {
            'batch_size': len(texts),
            'total_time': total_time,
            'avg_time_per_sentence': total_time / len(texts) if texts else 0,
            'total_tokens': sum(r['input_tokens'] for r in results),
            'avg_tokens_per_second': sum(r['tokens_per_second'] for r in results) / len(results) if results else 0
        }
        
        return results, batch_stats
    
    def save_translations(self, results: List[Dict], output_file: str):
        """Save translation results to file."""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Translations saved to: {output_path}")


class TranslationPipeline:
    """Complete translation pipeline with preprocessing and postprocessing."""
    
    def __init__(self, tokenizer_path: str, device: str = "cpu"):
        """Initialize the translation pipeline."""
        self.translator = SimpleTranslator(tokenizer_path, device)
    
    def preprocess_text(self, text: str) -> str:
        """Preprocess input text."""
        # Basic text cleaning
        text = text.strip()
        
        # Normalize whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        return text
    
    def postprocess_text(self, text: str) -> str:
        """Postprocess translated text."""
        # Basic postprocessing
        text = text.strip()
        
        # Capitalize first letter if it's a sentence
        if text and text[0].islower():
            text = text[0].upper() + text[1:]
        
        return text
    
    def translate_text(self, text: str, direction: str = "en2iu") -> Dict[str, Union[str, float]]:
        """Translate text with pre/post-processing."""
        # Preprocess
        preprocessed_text = self.preprocess_text(text)
        
        # Translate
        result = self.translator.translate(preprocessed_text, direction)
        
        # Postprocess
        result['translation'] = self.postprocess_text(result['translation'])
        result['original_input'] = text
        result['preprocessed_input'] = preprocessed_text
        
        return result
    
    def translate_file(self, input_file: str, output_file: str, direction: str = "en2iu"):
        """Translate text from file."""
        input_path = Path(input_file)
        
        if not input_path.exists():
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Read input texts
        with open(input_path, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        
        logger.info(f"Translating {len(texts)} sentences from {input_file}")
        
        # Translate batch
        results, batch_stats = self.translator.translate_batch(texts, direction)
        
        # Save results
        output_data = {
            'metadata': {
                'input_file': str(input_file),
                'output_file': str(output_file),
                'direction': direction,
                'batch_stats': batch_stats
            },
            'translations': results
        }
        
        self.translator.save_translations(output_data, output_file)
        
        logger.info(f"Translation complete. Results saved to: {output_file}")
        logger.info(f"Batch statistics: {batch_stats}")
        
        return results, batch_stats


if __name__ == "__main__":
    # Example usage
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    
    # Initialize pipeline
    pipeline = TranslationPipeline(tokenizer_path)
    
    # Test translations
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "The Government of Nunavut is committed to improving education.",
        "Hello, how are you?",
        "Good morning, everyone."
    ]
    
    print("Testing English to Inuktitut translation:")
    print("=" * 50)
    
    for sentence in test_sentences:
        result = pipeline.translate_text(sentence, direction="en2iu")
        print(f"EN: {result['input_text']}")
        print(f"IU: {result['translation']}")
        print(f"Time: {result['inference_time']:.3f}s")
        print("-" * 30)
    
    # Test Inuktitut to English
    iu_sentences = [
        "ᖁᔭᓐᓇᒦᒃ, ᐃᒃᓯᕙᐅᑖᖅ.",
        "ᐊᐃ, ᖃᓄᐃᓕᖓᕙ?"
    ]
    
    print("\nTesting Inuktitut to English translation:")
    print("=" * 50)
    
    for sentence in iu_sentences:
        result = pipeline.translate_text(sentence, direction="iu2en")
        print(f"IU: {result['input_text']}")
        print(f"EN: {result['translation']}")
        print(f"Time: {result['inference_time']:.3f}s")
        print("-" * 30)
    
    print("\nTranslation pipeline test completed!")
