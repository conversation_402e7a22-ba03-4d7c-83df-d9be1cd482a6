#!/usr/bin/env python3
"""
Test script for the trained neural translation model.
"""

import torch
import sentencepiece as spm
from pathlib import Path
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_neural_model():
    """Test the trained neural model with various sentences."""
    
    # Check for trained model
    model_paths = [
        "nunavut_mt/models/improved_gpu/improved_translation_model.pt",
        "nunavut_mt/models/efficient_gpu/efficient_translation_model.pt"
    ]
    
    model_path = None
    for path in model_paths:
        if Path(path).exists():
            model_path = path
            break
    
    if not model_path:
        logger.error("No trained model found. Please run training first.")
        return
    
    logger.info(f"Using model: {model_path}")
    
    # Load tokenizer
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Import model class
    from train_efficient_gpu import EfficientTranslator
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']
    
    # Create model
    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    logger.info("Model loaded successfully!")
    
    # Test sentences
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "Hello, how are you?",
        "Good morning.",
        "The government is working.",
        "I am happy.",
        "Welcome to Nunavut.",
        "How are you today?",
        "The weather is nice.",
        "I love you.",
        "See you later."
    ]
    
    print("\n" + "="*70)
    print("🚀 NEURAL MODEL TRANSLATION TESTS")
    print("="*70)
    
    with torch.no_grad():
        for i, sentence in enumerate(test_sentences, 1):
            print(f"\n{i}. Testing: '{sentence}'")
            
            # Test EN → IU
            result_en2iu = translate_with_model(model, tokenizer, sentence, "en2iu", device)
            print(f"   EN → IU: {result_en2iu}")
            
            # Test IU → EN (translate back)
            if result_en2iu and result_en2iu.strip():
                result_iu2en = translate_with_model(model, tokenizer, result_en2iu, "iu2en", device)
                print(f"   IU → EN: {result_iu2en}")
            
            print("-" * 50)


def translate_with_model(model, tokenizer, text, direction, device):
    """Translate text using the neural model."""
    try:
        # Prepare input
        if direction == "en2iu":
            input_text = f"<en2iu> {text}"
        else:
            input_text = f"<iu2en> {text}"
        
        # Tokenize
        input_tokens = tokenizer.encode_as_ids(input_text)
        if len(input_tokens) > 32:
            input_tokens = input_tokens[:32]
        
        src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
        
        # Generate translation with improved decoding
        max_len = 32
        output_tokens = [2]  # BOS token
        
        for _ in range(max_len):
            tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
            
            outputs = model(src_tensor, tgt_tensor)
            logits = outputs[0, -1]
            
            # Use different strategies for better output
            if len(output_tokens) < 5:
                # Early in generation, use top-k sampling
                temperature = 0.9
                top_k = 15
                
                logits = logits / temperature
                top_logits, top_indices = torch.topk(logits, top_k)
                probs = torch.softmax(top_logits, dim=-1)
                next_token_idx = torch.multinomial(probs, 1).item()
                next_token = top_indices[next_token_idx].item()
            else:
                # Later in generation, be more conservative
                temperature = 0.7
                top_k = 8
                
                logits = logits / temperature
                top_logits, top_indices = torch.topk(logits, top_k)
                probs = torch.softmax(top_logits, dim=-1)
                next_token_idx = torch.multinomial(probs, 1).item()
                next_token = top_indices[next_token_idx].item()
            
            if next_token == 3:  # EOS token
                break
            
            output_tokens.append(next_token)
        
        # Decode output
        if len(output_tokens) > 1:
            result = tokenizer.decode_ids(output_tokens[1:])  # Skip BOS
            # Clean up
            result = result.replace('<en2iu>', '').replace('<iu2en>', '').strip()
            # Remove any remaining special tokens
            result = result.replace('<unk>', '').replace('<s>', '').replace('</s>', '').strip()
            return result if result else "[Empty output]"
        else:
            return "[No output generated]"
            
    except Exception as e:
        logger.error(f"Translation failed: {e}")
        return f"[Error: {e}]"


def interactive_mode():
    """Interactive translation mode."""

    # Load model (same as above)
    model_paths = [
        "nunavut_mt/models/improved_gpu/improved_translation_model.pt",
        "nunavut_mt/models/efficient_gpu/efficient_translation_model.pt"
    ]

    model_path = None
    for path in model_paths:
        if Path(path).exists():
            model_path = path
            break

    if not model_path:
        print("No trained model found. Please run training first.")
        return

    # Load everything
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    from train_efficient_gpu import EfficientTranslator

    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']

    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()

    print("\n🚀 Interactive Neural Translation Mode")
    print("Commands: 'en2iu <text>' or 'iu2en <text>' or 'quit'")
    print("Example: en2iu Hello, how are you?")
    print("-" * 50)

    while True:
        try:
            # Check if we're in a pipe/non-interactive environment
            if not sys.stdin.isatty():
                print("Interactive mode requires a terminal. Exiting.")
                break

            user_input = input("\n> ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break

            if user_input.startswith('en2iu '):
                text = user_input[6:]
                result = translate_with_model(model, tokenizer, text, "en2iu", device)
                print(f"EN → IU: {result}")

            elif user_input.startswith('iu2en '):
                text = user_input[6:]
                result = translate_with_model(model, tokenizer, text, "iu2en", device)
                print(f"IU → EN: {result}")

            else:
                print("Please use format: 'en2iu <text>' or 'iu2en <text>'")

        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except EOFError:
            print("\nInput stream ended. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")
            break


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        test_neural_model()
