#!/usr/bin/env python3
"""
Progressive Neural Machine Translation Trainer
Implements the comprehensive training plan for English-Inuktitut translation.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
from transformers import (
    AutoTokenizer, AutoModel, 
    get_cosine_schedule_with_warmup,
    get_linear_schedule_with_warmup
)
import sentencepiece as spm
import json
import logging
from pathlib import Path
import time
import math
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import numpy as np
from tqdm import tqdm

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TrainingStage:
    """Configuration for each training stage."""
    name: str
    dataset_size: int
    model_params: int
    epochs: int
    batch_size: int
    learning_rate: float
    hidden_size: int
    num_layers: int
    num_heads: int
    target_bleu: float
    estimated_hours: int
    memory_gb: float


class ProgressiveTransformer(nn.Module):
    """Scalable transformer model for progressive training."""
    
    def __init__(self, vocab_size: int, hidden_size: int, num_layers: int, 
                 num_heads: int, max_seq_len: int = 128, dropout: float = 0.1):
        super().__init__()
        
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.src_embedding = nn.Embedding(vocab_size, hidden_size)
        self.tgt_embedding = nn.Embedding(vocab_size, hidden_size)
        self.pos_encoding = self._create_positional_encoding(max_seq_len, hidden_size)
        
        # Transformer layers
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            batch_first=True
        )
        self.encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 4,
            dropout=dropout,
            batch_first=True
        )
        self.decoder = nn.TransformerDecoder(decoder_layer, num_layers)
        
        # Output projection
        self.output_projection = nn.Linear(hidden_size, vocab_size)
        
        # Initialize weights
        self._init_weights()
    
    def _create_positional_encoding(self, max_len: int, d_model: int) -> torch.Tensor:
        """Create sinusoidal positional encoding."""
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len).unsqueeze(1).float()
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           -(math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        return pe.unsqueeze(0)
    
    def _init_weights(self):
        """Initialize model weights."""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, src: torch.Tensor, tgt: torch.Tensor, 
                src_mask: Optional[torch.Tensor] = None,
                tgt_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass."""
        
        batch_size, src_len = src.shape
        _, tgt_len = tgt.shape
        
        # Embeddings + positional encoding
        src_emb = self.src_embedding(src) + self.pos_encoding[:, :src_len, :].to(src.device)
        tgt_emb = self.tgt_embedding(tgt) + self.pos_encoding[:, :tgt_len, :].to(tgt.device)
        
        # Create causal mask for decoder
        if tgt_mask is None:
            tgt_mask = self._create_causal_mask(tgt_len).to(tgt.device)
        
        # Encoder
        encoder_output = self.encoder(src_emb, src_key_padding_mask=src_mask)
        
        # Decoder
        decoder_output = self.decoder(
            tgt_emb, encoder_output,
            tgt_mask=tgt_mask,
            tgt_key_padding_mask=None,
            memory_key_padding_mask=src_mask
        )
        
        # Output projection
        logits = self.output_projection(decoder_output)
        
        return logits
    
    def _create_causal_mask(self, size: int) -> torch.Tensor:
        """Create causal mask for decoder."""
        mask = torch.triu(torch.ones(size, size), diagonal=1)
        return mask.bool()


class ProgressiveTrainer:
    """Progressive training manager."""
    
    def __init__(self, tokenizer_path: str, data_dir: str, output_dir: str):
        self.tokenizer_path = tokenizer_path
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load tokenizer
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.load(tokenizer_path)
        self.vocab_size = self.tokenizer.vocab_size()
        
        # Define training stages
        self.stages = self._define_training_stages()
        
        # Device setup
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")
        
        if torch.cuda.is_available():
            logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    def _define_training_stages(self) -> List[TrainingStage]:
        """Define the progressive training stages."""
        return [
            TrainingStage(
                name="foundation",
                dataset_size=5000,
                model_params=12_000_000,
                epochs=15,
                batch_size=32,
                learning_rate=1e-4,
                hidden_size=256,
                num_layers=3,
                num_heads=4,
                target_bleu=5.0,
                estimated_hours=8,
                memory_gb=2.5
            ),
            TrainingStage(
                name="enhancement",
                dataset_size=25000,
                model_params=25_000_000,
                epochs=20,
                batch_size=24,
                learning_rate=8e-5,
                hidden_size=320,
                num_layers=4,
                num_heads=8,
                target_bleu=12.0,
                estimated_hours=16,
                memory_gb=4.5
            ),
            TrainingStage(
                name="scale_up",
                dataset_size=100000,
                model_params=50_000_000,
                epochs=25,
                batch_size=16,
                learning_rate=5e-5,
                hidden_size=512,
                num_layers=6,
                num_heads=8,
                target_bleu=20.0,
                estimated_hours=40,
                memory_gb=8.0
            ),
            TrainingStage(
                name="production",
                dataset_size=500000,
                model_params=100_000_000,
                epochs=30,
                batch_size=8,
                learning_rate=3e-5,
                hidden_size=512,
                num_layers=8,
                num_heads=16,
                target_bleu=30.0,
                estimated_hours=80,
                memory_gb=11.0
            ),
            TrainingStage(
                name="full_corpus",
                dataset_size=1150000,
                model_params=150_000_000,
                epochs=20,
                batch_size=4,
                learning_rate=2e-5,
                hidden_size=640,
                num_layers=10,
                num_heads=16,
                target_bleu=40.0,
                estimated_hours=120,
                memory_gb=12.0
            )
        ]
    
    def prepare_data(self, stage: TrainingStage) -> Tuple[DataLoader, DataLoader]:
        """Prepare training and validation data for a stage."""
        
        logger.info(f"Preparing data for stage: {stage.name}")
        logger.info(f"Target dataset size: {stage.dataset_size:,}")
        
        # Load and filter data based on stage requirements
        train_data = self._load_filtered_data(stage.dataset_size, split='train')
        val_data = self._load_filtered_data(min(stage.dataset_size // 10, 5000), split='val')
        
        # Create datasets
        train_dataset = TranslationDataset(train_data, self.tokenizer)
        val_dataset = TranslationDataset(val_data, self.tokenizer)
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=stage.batch_size,
            shuffle=True,
            collate_fn=self._collate_fn,
            num_workers=2,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=stage.batch_size,
            shuffle=False,
            collate_fn=self._collate_fn,
            num_workers=2,
            pin_memory=True
        )
        
        logger.info(f"Training samples: {len(train_dataset):,}")
        logger.info(f"Validation samples: {len(val_dataset):,}")
        
        return train_loader, val_loader
    
    def _load_filtered_data(self, size: int, split: str) -> List[Tuple[str, str]]:
        """Load and filter data based on quality criteria."""
        
        # Load raw data
        en_file = self.data_dir / f"{split}.en"
        iu_file = self.data_dir / f"{split}.iu"
        
        with open(en_file, 'r', encoding='utf-8') as f:
            en_lines = [line.strip() for line in f]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_lines = [line.strip() for line in f]
        
        # Filter and select high-quality pairs
        pairs = []
        for en, iu in zip(en_lines, iu_lines):
            if self._is_high_quality_pair(en, iu):
                pairs.append((en, iu))
            
            if len(pairs) >= size:
                break
        
        logger.info(f"Selected {len(pairs):,} high-quality pairs from {len(en_lines):,} total")
        return pairs
    
    def _is_high_quality_pair(self, en: str, iu: str) -> bool:
        """Check if a sentence pair meets quality criteria."""
        
        # Length filters
        en_words = len(en.split())
        iu_words = len(iu.split())
        
        if en_words < 3 or en_words > 50:
            return False
        if iu_words < 2 or iu_words > 60:
            return False
        
        # Length ratio filter
        ratio = max(en_words, iu_words) / min(en_words, iu_words)
        if ratio > 3.0:
            return False
        
        # Character filters
        if len(en) < 10 or len(iu) < 5:
            return False
        
        # Basic content filters
        if en.count('.') > 3 or iu.count('.') > 3:  # Too many sentences
            return False
        
        return True
    
    def _collate_fn(self, batch: List[Tuple[torch.Tensor, torch.Tensor]]) -> Dict[str, torch.Tensor]:
        """Collate function for data loader."""
        
        src_batch, tgt_batch = zip(*batch)
        
        # Pad sequences
        src_padded = torch.nn.utils.rnn.pad_sequence(src_batch, batch_first=True, padding_value=0)
        tgt_padded = torch.nn.utils.rnn.pad_sequence(tgt_batch, batch_first=True, padding_value=0)
        
        # Create masks
        src_mask = (src_padded == 0)
        
        return {
            'src': src_padded,
            'tgt': tgt_padded,
            'src_mask': src_mask
        }
    
    def train_stage(self, stage: TrainingStage, previous_model_path: Optional[str] = None) -> str:
        """Train a single stage."""
        
        logger.info(f"🚀 Starting Stage: {stage.name}")
        logger.info(f"Target BLEU: {stage.target_bleu}")
        logger.info(f"Estimated time: {stage.estimated_hours} hours")
        logger.info(f"Memory requirement: {stage.memory_gb} GB")
        
        # Check memory requirements
        if torch.cuda.is_available():
            available_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            if stage.memory_gb > available_memory * 0.9:
                logger.warning(f"Memory requirement ({stage.memory_gb} GB) close to limit ({available_memory:.1f} GB)")
        
        # Prepare data
        train_loader, val_loader = self.prepare_data(stage)
        
        # Create model
        model = ProgressiveTransformer(
            vocab_size=self.vocab_size,
            hidden_size=stage.hidden_size,
            num_layers=stage.num_layers,
            num_heads=stage.num_heads,
            max_seq_len=128
        ).to(self.device)
        
        # Load previous model if available
        if previous_model_path and Path(previous_model_path).exists():
            logger.info(f"Loading previous model: {previous_model_path}")
            checkpoint = torch.load(previous_model_path, map_location=self.device)
            # Only load compatible layers
            self._load_compatible_weights(model, checkpoint['model_state_dict'])
        
        # Setup training
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=stage.learning_rate,
            betas=(0.9, 0.98),
            eps=1e-9,
            weight_decay=1e-4
        )
        
        total_steps = len(train_loader) * stage.epochs
        scheduler = get_cosine_schedule_with_warmup(
            optimizer,
            num_warmup_steps=min(1000, total_steps // 10),
            num_training_steps=total_steps
        )
        
        # Training loop
        best_bleu = 0.0
        patience_counter = 0
        
        for epoch in range(stage.epochs):
            # Training
            model.train()
            train_loss = self._train_epoch(model, train_loader, optimizer, scheduler, epoch)
            
            # Validation
            model.eval()
            val_loss, bleu_score = self._validate_epoch(model, val_loader, epoch)
            
            logger.info(f"Epoch {epoch+1}/{stage.epochs}")
            logger.info(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}, BLEU: {bleu_score:.2f}")
            
            # Save best model
            if bleu_score > best_bleu:
                best_bleu = bleu_score
                patience_counter = 0
                
                model_path = self.output_dir / f"{stage.name}_best_model.pt"
                self._save_model(model, optimizer, scheduler, epoch, bleu_score, model_path)
                logger.info(f"New best BLEU: {bleu_score:.2f}")
            else:
                patience_counter += 1
            
            # Early stopping
            if patience_counter >= 3 and epoch > stage.epochs // 2:
                logger.info(f"Early stopping at epoch {epoch+1}")
                break
            
            # Target reached
            if bleu_score >= stage.target_bleu:
                logger.info(f"Target BLEU {stage.target_bleu} reached!")
                break
        
        logger.info(f"✅ Stage {stage.name} completed. Best BLEU: {best_bleu:.2f}")
        return str(self.output_dir / f"{stage.name}_best_model.pt")
    
    def _train_epoch(self, model, train_loader, optimizer, scheduler, epoch):
        """Train for one epoch."""
        total_loss = 0.0
        num_batches = len(train_loader)
        
        progress_bar = tqdm(train_loader, desc=f"Training Epoch {epoch+1}")
        
        for batch_idx, batch in enumerate(progress_bar):
            src = batch['src'].to(self.device)
            tgt = batch['tgt'].to(self.device)
            src_mask = batch['src_mask'].to(self.device)
            
            # Prepare decoder input and target
            tgt_input = tgt[:, :-1]
            tgt_output = tgt[:, 1:]
            
            # Forward pass
            optimizer.zero_grad()
            logits = model(src, tgt_input, src_mask=src_mask)
            
            # Calculate loss with label smoothing
            loss = self._calculate_loss(logits, tgt_output)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            scheduler.step()
            
            total_loss += loss.item()
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'avg_loss': f'{total_loss/(batch_idx+1):.4f}',
                'lr': f'{scheduler.get_last_lr()[0]:.2e}'
            })
        
        return total_loss / num_batches
    
    def _validate_epoch(self, model, val_loader, epoch):
        """Validate for one epoch."""
        total_loss = 0.0
        num_batches = len(val_loader)
        
        # For BLEU calculation
        predictions = []
        references = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Validation Epoch {epoch+1}"):
                src = batch['src'].to(self.device)
                tgt = batch['tgt'].to(self.device)
                src_mask = batch['src_mask'].to(self.device)
                
                # Calculate loss
                tgt_input = tgt[:, :-1]
                tgt_output = tgt[:, 1:]
                logits = model(src, tgt_input, src_mask=src_mask)
                loss = self._calculate_loss(logits, tgt_output)
                total_loss += loss.item()
                
                # Generate translations for BLEU
                if len(predictions) < 100:  # Sample for BLEU calculation
                    generated = self._generate_translation(model, src[:1], src_mask[:1])
                    pred_text = self.tokenizer.decode_ids(generated[0])
                    ref_text = self.tokenizer.decode_ids(tgt[0].cpu().tolist())
                    
                    predictions.append(pred_text)
                    references.append(ref_text)
        
        # Calculate BLEU score
        bleu_score = self._calculate_bleu(predictions, references)
        
        return total_loss / num_batches, bleu_score
    
    def _generate_translation(self, model, src, src_mask, max_length=50):
        """Generate translation using beam search."""
        batch_size = src.size(0)
        
        # Start with BOS token
        generated = torch.full((batch_size, 1), 2, dtype=torch.long, device=self.device)  # BOS = 2
        
        for _ in range(max_length):
            logits = model(src, generated, src_mask=src_mask)
            next_token_logits = logits[:, -1, :]
            
            # Prevent immediate EOS for first few tokens
            if generated.size(1) <= 3:
                next_token_logits[:, 3] = -float('inf')  # Block EOS
            
            # Sample next token
            next_token = torch.argmax(next_token_logits, dim=-1, keepdim=True)
            generated = torch.cat([generated, next_token], dim=1)
            
            # Stop if EOS generated
            if next_token.item() == 3:  # EOS = 3
                break
        
        return generated.cpu().tolist()
    
    def _calculate_loss(self, logits, targets):
        """Calculate loss with label smoothing."""
        # Reshape for cross entropy
        logits = logits.reshape(-1, logits.size(-1))
        targets = targets.reshape(-1)
        
        # Ignore padding tokens
        mask = targets != 0
        logits = logits[mask]
        targets = targets[mask]
        
        # Label smoothing
        eps = 0.1
        n_class = logits.size(-1)
        one_hot = torch.zeros_like(logits).scatter(1, targets.unsqueeze(1), 1)
        one_hot = one_hot * (1 - eps) + (1 - one_hot) * eps / (n_class - 1)
        log_prb = F.log_softmax(logits, dim=1)
        
        loss = -(one_hot * log_prb).sum(dim=1).mean()
        return loss
    
    def _calculate_bleu(self, predictions, references):
        """Calculate BLEU score."""
        # Simple BLEU approximation
        # In practice, use sacrebleu library
        total_score = 0.0
        
        for pred, ref in zip(predictions, references):
            pred_tokens = pred.split()
            ref_tokens = ref.split()
            
            if len(pred_tokens) == 0:
                continue
            
            # Simple 1-gram precision
            matches = sum(1 for token in pred_tokens if token in ref_tokens)
            precision = matches / len(pred_tokens) if pred_tokens else 0
            
            # Length penalty
            bp = min(1.0, len(pred_tokens) / max(len(ref_tokens), 1))
            
            score = precision * bp
            total_score += score
        
        return (total_score / len(predictions)) * 100 if predictions else 0.0
    
    def _load_compatible_weights(self, model, state_dict):
        """Load compatible weights from previous model."""
        model_dict = model.state_dict()
        
        # Filter compatible weights
        compatible_dict = {}
        for k, v in state_dict.items():
            if k in model_dict and model_dict[k].shape == v.shape:
                compatible_dict[k] = v
                logger.info(f"Loaded: {k}")
            else:
                logger.info(f"Skipped: {k} (shape mismatch or not found)")
        
        model_dict.update(compatible_dict)
        model.load_state_dict(model_dict)
    
    def _save_model(self, model, optimizer, scheduler, epoch, bleu_score, path):
        """Save model checkpoint."""
        torch.save({
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'epoch': epoch,
            'bleu_score': bleu_score,
            'vocab_size': self.vocab_size
        }, path)
    
    def run_progressive_training(self):
        """Run the complete progressive training pipeline."""
        
        logger.info("🚀 Starting Progressive Neural Machine Translation Training")
        logger.info(f"Total stages: {len(self.stages)}")
        
        previous_model_path = None
        
        for i, stage in enumerate(self.stages):
            logger.info(f"\n{'='*60}")
            logger.info(f"STAGE {i+1}/{len(self.stages)}: {stage.name.upper()}")
            logger.info(f"{'='*60}")
            
            try:
                model_path = self.train_stage(stage, previous_model_path)
                previous_model_path = model_path
                
                logger.info(f"✅ Stage {stage.name} completed successfully")
                logger.info(f"Model saved: {model_path}")
                
            except Exception as e:
                logger.error(f"❌ Stage {stage.name} failed: {e}")
                raise
        
        logger.info("\n🎉 Progressive training completed successfully!")
        logger.info(f"Final model: {previous_model_path}")


class TranslationDataset(Dataset):
    """Dataset for translation pairs."""
    
    def __init__(self, pairs: List[Tuple[str, str]], tokenizer):
        self.pairs = pairs
        self.tokenizer = tokenizer
    
    def __len__(self):
        return len(self.pairs)
    
    def __getitem__(self, idx):
        en_text, iu_text = self.pairs[idx]
        
        # Tokenize
        en_tokens = self.tokenizer.encode_as_ids(f"<en2iu> {en_text}")
        iu_tokens = [2] + self.tokenizer.encode_as_ids(iu_text) + [3]  # BOS + text + EOS
        
        # Convert to tensors
        src = torch.tensor(en_tokens, dtype=torch.long)
        tgt = torch.tensor(iu_tokens, dtype=torch.long)
        
        return src, tgt


if __name__ == "__main__":
    # Configuration
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    data_dir = "nunavut_mt/data/processed"
    output_dir = "nunavut_mt/models/progressive"
    
    # Create trainer
    trainer = ProgressiveTrainer(tokenizer_path, data_dir, output_dir)
    
    # Run progressive training
    trainer.run_progressive_training()
