"""
Corpus Analysis Module for Nunavut Hansard English-Inuktitut Parallel Corpus

This module provides tools to analyze the structure, statistics, and quality
of the parallel corpus data.
"""

import os
import re
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
from typing import List, Tuple, Dict, Optional
import unicodedata
from pathlib import Path


class CorpusAnalyzer:
    """Analyzer for the Nunavut Hansard parallel corpus."""
    
    def __init__(self, corpus_dir: str):
        """
        Initialize the corpus analyzer.
        
        Args:
            corpus_dir: Path to the corpus directory containing split files
        """
        self.corpus_dir = Path(corpus_dir)
        self.split_dir = self.corpus_dir / "split"
        self.stats = {}
        
    def load_parallel_data(self, split: str = "train") -> Tuple[List[str], List[str], List[str]]:
        """
        Load parallel data for a given split.
        
        Args:
            split: Data split to load (train, dev, test, dev-dedup, etc.)
            
        Returns:
            Tuple of (english_sentences, inuktitut_sentences, ids)
        """
        en_file = self.split_dir / f"{split}.en"
        iu_file = self.split_dir / f"{split}.iu"
        id_file = self.split_dir / f"{split}.id"
        
        # Read files
        with open(en_file, 'r', encoding='utf-8') as f:
            en_sentences = [line.strip() for line in f]
            
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_sentences = [line.strip() for line in f]
            
        with open(id_file, 'r', encoding='utf-8') as f:
            ids = [line.strip() for line in f]
            
        assert len(en_sentences) == len(iu_sentences) == len(ids), \
            f"Mismatched lengths: EN={len(en_sentences)}, IU={len(iu_sentences)}, ID={len(ids)}"
            
        return en_sentences, iu_sentences, ids
    
    def analyze_basic_statistics(self, split: str = "train") -> Dict:
        """
        Analyze basic corpus statistics.
        
        Args:
            split: Data split to analyze
            
        Returns:
            Dictionary containing basic statistics
        """
        en_sentences, iu_sentences, ids = self.load_parallel_data(split)
        
        stats = {
            'total_pairs': len(en_sentences),
            'empty_en': sum(1 for s in en_sentences if not s.strip()),
            'empty_iu': sum(1 for s in iu_sentences if not s.strip()),
            'empty_both': sum(1 for en, iu in zip(en_sentences, iu_sentences) 
                            if not en.strip() and not iu.strip()),
        }
        
        # Filter out empty sentences for length analysis
        non_empty_pairs = [(en, iu) for en, iu in zip(en_sentences, iu_sentences) 
                          if en.strip() and iu.strip()]
        
        if non_empty_pairs:
            en_lengths = [len(en.split()) for en, _ in non_empty_pairs]
            iu_lengths = [len(iu.split()) for _, iu in non_empty_pairs]
            
            stats.update({
                'non_empty_pairs': len(non_empty_pairs),
                'en_avg_length': np.mean(en_lengths),
                'en_median_length': np.median(en_lengths),
                'en_max_length': max(en_lengths),
                'en_min_length': min(en_lengths),
                'iu_avg_length': np.mean(iu_lengths),
                'iu_median_length': np.median(iu_lengths),
                'iu_max_length': max(iu_lengths),
                'iu_min_length': min(iu_lengths),
                'length_ratio_avg': np.mean([iu/en if en > 0 else 0 for en, iu in zip(en_lengths, iu_lengths)]),
            })
        
        self.stats[split] = stats
        return stats
    
    def analyze_vocabulary(self, split: str = "train") -> Dict:
        """
        Analyze vocabulary statistics for both languages.
        
        Args:
            split: Data split to analyze
            
        Returns:
            Dictionary containing vocabulary statistics
        """
        en_sentences, iu_sentences, _ = self.load_parallel_data(split)
        
        # Tokenize and count
        en_tokens = []
        iu_tokens = []
        
        for en, iu in zip(en_sentences, iu_sentences):
            if en.strip() and iu.strip():
                en_tokens.extend(en.lower().split())
                iu_tokens.extend(iu.split())  # Don't lowercase Inuktitut syllabics
        
        en_vocab = Counter(en_tokens)
        iu_vocab = Counter(iu_tokens)
        
        vocab_stats = {
            'en_total_tokens': len(en_tokens),
            'en_unique_tokens': len(en_vocab),
            'en_vocab_size': len(en_vocab),
            'iu_total_tokens': len(iu_tokens),
            'iu_unique_tokens': len(iu_vocab),
            'iu_vocab_size': len(iu_vocab),
            'en_most_common': en_vocab.most_common(20),
            'iu_most_common': iu_vocab.most_common(20),
        }
        
        return vocab_stats
    
    def analyze_character_distribution(self, split: str = "train") -> Dict:
        """
        Analyze character distribution, especially for Inuktitut syllabics.
        
        Args:
            split: Data split to analyze
            
        Returns:
            Dictionary containing character distribution statistics
        """
        _, iu_sentences, _ = self.load_parallel_data(split)
        
        # Collect all Inuktitut characters
        iu_chars = []
        for sentence in iu_sentences:
            if sentence.strip():
                iu_chars.extend(list(sentence))
        
        char_counter = Counter(iu_chars)
        
        # Categorize characters
        syllabics = []
        latin = []
        punctuation = []
        digits = []
        other = []
        
        for char, count in char_counter.items():
            if '\u1400' <= char <= '\u167F':  # Unified Canadian Aboriginal Syllabics
                syllabics.append((char, count))
            elif char.isalpha():
                latin.append((char, count))
            elif char in '.,!?;:()[]{}"\'-':
                punctuation.append((char, count))
            elif char.isdigit():
                digits.append((char, count))
            else:
                other.append((char, count))
        
        char_stats = {
            'total_chars': len(iu_chars),
            'unique_chars': len(char_counter),
            'syllabics_count': sum(count for _, count in syllabics),
            'latin_count': sum(count for _, count in latin),
            'punctuation_count': sum(count for _, count in punctuation),
            'digits_count': sum(count for _, count in digits),
            'other_count': sum(count for _, count in other),
            'syllabics_chars': sorted(syllabics, key=lambda x: x[1], reverse=True)[:50],
            'most_common_chars': char_counter.most_common(50),
        }
        
        return char_stats
    
    def generate_report(self, output_dir: str = "analysis_output"):
        """
        Generate a comprehensive analysis report.
        
        Args:
            output_dir: Directory to save analysis outputs
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Analyze all splits
        splits = ["train", "dev-dedup", "devtest-dedup", "test-dedup"]
        all_stats = {}
        
        for split in splits:
            print(f"Analyzing {split} split...")
            try:
                basic_stats = self.analyze_basic_statistics(split)
                vocab_stats = self.analyze_vocabulary(split)
                char_stats = self.analyze_character_distribution(split)
                
                all_stats[split] = {
                    'basic': basic_stats,
                    'vocabulary': vocab_stats,
                    'characters': char_stats
                }
            except FileNotFoundError:
                print(f"Warning: {split} split not found, skipping...")
                continue
        
        # Save detailed statistics
        import json
        with open(output_path / "corpus_statistics.json", 'w', encoding='utf-8') as f:
            json.dump(all_stats, f, indent=2, ensure_ascii=False)
        
        # Generate summary report
        self._generate_summary_report(all_stats, output_path)
        
        print(f"Analysis complete. Results saved to {output_path}")
    
    def _generate_summary_report(self, all_stats: Dict, output_path: Path):
        """Generate a human-readable summary report."""
        
        report_lines = [
            "# Nunavut Hansard Corpus Analysis Report",
            "",
            "## Dataset Overview",
            ""
        ]
        
        # Basic statistics table
        if 'train' in all_stats:
            train_stats = all_stats['train']['basic']
            report_lines.extend([
                f"- **Training pairs**: {train_stats['total_pairs']:,}",
                f"- **Non-empty pairs**: {train_stats['non_empty_pairs']:,}",
                f"- **Empty English sentences**: {train_stats['empty_en']:,}",
                f"- **Empty Inuktitut sentences**: {train_stats['empty_iu']:,}",
                "",
                "## Sentence Length Statistics (Training Set)",
                "",
                f"- **English average length**: {train_stats['en_avg_length']:.1f} words",
                f"- **Inuktitut average length**: {train_stats['iu_avg_length']:.1f} words",
                f"- **Length ratio (IU/EN)**: {train_stats['length_ratio_avg']:.2f}",
                ""
            ])
        
        # Vocabulary statistics
        if 'train' in all_stats:
            vocab_stats = all_stats['train']['vocabulary']
            report_lines.extend([
                "## Vocabulary Statistics (Training Set)",
                "",
                f"- **English vocabulary size**: {vocab_stats['en_vocab_size']:,}",
                f"- **Inuktitut vocabulary size**: {vocab_stats['iu_vocab_size']:,}",
                f"- **English total tokens**: {vocab_stats['en_total_tokens']:,}",
                f"- **Inuktitut total tokens**: {vocab_stats['iu_total_tokens']:,}",
                ""
            ])
        
        # Character distribution
        if 'train' in all_stats:
            char_stats = all_stats['train']['characters']
            report_lines.extend([
                "## Character Distribution (Inuktitut)",
                "",
                f"- **Total characters**: {char_stats['total_chars']:,}",
                f"- **Syllabics characters**: {char_stats['syllabics_count']:,} ({char_stats['syllabics_count']/char_stats['total_chars']*100:.1f}%)",
                f"- **Latin characters**: {char_stats['latin_count']:,} ({char_stats['latin_count']/char_stats['total_chars']*100:.1f}%)",
                f"- **Punctuation**: {char_stats['punctuation_count']:,} ({char_stats['punctuation_count']/char_stats['total_chars']*100:.1f}%)",
                ""
            ])
        
        # Write report
        with open(output_path / "analysis_report.md", 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))


if __name__ == "__main__":
    # Example usage
    analyzer = CorpusAnalyzer("../../../")  # Adjust path as needed
    analyzer.generate_report()
