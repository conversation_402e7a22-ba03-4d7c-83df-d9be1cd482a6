#!/usr/bin/env python
# -*- coding: utf-8 -*-

# @file track_removal.py
# @brief Get a list of IDs of the remaining lines after cleaning.
#
# <AUTHOR>
#
# Traitement multilingue de textes / Multilingual Text Processing
# Centre de recherche en technologies numériques / Digital Technologies Research Centre
# Conseil national de recherches Canada / National Research Council Canada
# Copyright 2020, Sa Majeste la Reine du Chef du Canada /
# Copyright 2020, Her Majesty in Right of Canada

"""
Given an ID file, the original file, and the cleaned file, print line IDs for
only the lines in the cleaned file.
ID file should be from raw data, original file should be from tok, cleaned file from clean.
"""

from __future__ import print_function, unicode_literals

import sys
from argparse import ArgumentParser

def get_args():
   """Command line argument processing."""

   usage="track_removal.py [-h] id_filename orig_filename clean_filename"
   help="""
   Given an ID file, the original file, and the cleaned file, print line IDs for
   only the lines in the cleaned file.
   ID file should be from raw data, original file should be from tok,
   cleaned file should be from clean.
   """

   # Use the argparse module, not the deprecated optparse module.
   parser = ArgumentParser(usage=usage, description=help, add_help=True)

   parser.add_argument("id_filename", type=str, help="ID file")
   parser.add_argument("orig_filename", type=str, help="Original file")
   parser.add_argument("clean_filename", type=str, help="Cleaned file")

   cmd_args = parser.parse_args()
   return cmd_args

def main():
   cmd_args = get_args()
   
   with open(cmd_args.id_filename) as idfile, \
        open(cmd_args.orig_filename) as origfile, \
        open(cmd_args.clean_filename) as cleanfile:
      for cleanline in cleanfile.readlines():
         idline, origline = idfile.readline(), origfile.readline()
         while origline != cleanline:
            idline, origline = idfile.readline(), origfile.readline()
         print(idline.strip())


if __name__ == '__main__':
   main()
