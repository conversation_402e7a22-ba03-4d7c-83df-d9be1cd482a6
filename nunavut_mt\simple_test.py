#!/usr/bin/env python3
"""
Simple test script to verify the neural model is working.
"""

import torch
import sentencepiece as spm
from pathlib import Path
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def simple_test():
    """Simple test of the trained model."""
    
    print("🔍 Testing Nunavut Neural Translation Model")
    print("=" * 50)
    
    # Check for models
    model_paths = [
        "nunavut_mt/models/improved_gpu/improved_translation_model.pt",
        "nunavut_mt/models/efficient_gpu/efficient_translation_model.pt"
    ]
    
    model_path = None
    for path in model_paths:
        if Path(path).exists():
            model_path = path
            print(f"✅ Found model: {path}")
            break
    
    if not model_path:
        print("❌ No trained model found!")
        print("Please run training first:")
        print("  python nunavut_mt/train_efficient_gpu.py")
        return False
    
    # Check GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️  Using device: {device}")
    if torch.cuda.is_available():
        print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    
    # Load tokenizer
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    if not Path(tokenizer_path).exists():
        print(f"❌ Tokenizer not found: {tokenizer_path}")
        return False
    
    print(f"✅ Loading tokenizer: {tokenizer_path}")
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    try:
        from train_efficient_gpu import EfficientTranslator
        
        print(f"✅ Loading model: {model_path}")
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        vocab_size = checkpoint['vocab_size']
        config = checkpoint['model_config']
        
        model = EfficientTranslator(
            vocab_size=vocab_size,
            d_model=config['d_model'],
            nhead=config['nhead'],
            num_layers=config['num_layers'],
            max_seq_len=config['max_seq_len']
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(device)
        model.eval()
        
        print(f"✅ Model loaded successfully!")
        print(f"   - Vocabulary size: {vocab_size:,}")
        print(f"   - Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        print(f"   - Architecture: {config}")
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return False
    
    # Test basic functionality
    print("\n🧪 Testing Basic Functionality")
    print("-" * 30)
    
    test_sentences = [
        "Thank you",
        "Hello",
        "Good morning"
    ]
    
    success_count = 0
    
    with torch.no_grad():
        for i, sentence in enumerate(test_sentences, 1):
            try:
                print(f"\n{i}. Testing: '{sentence}'")
                
                # Tokenize
                input_text = f"<en2iu> {sentence}"
                input_tokens = tokenizer.encode_as_ids(input_text)
                
                if len(input_tokens) > 32:
                    input_tokens = input_tokens[:32]
                
                src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
                
                # Generate
                max_len = 16
                output_tokens = [2]  # BOS
                
                for _ in range(max_len):
                    tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
                    outputs = model(src_tensor, tgt_tensor)
                    next_token = outputs[0, -1].argmax().item()
                    
                    if next_token == 3:  # EOS
                        break
                    
                    output_tokens.append(next_token)
                
                # Decode
                if len(output_tokens) > 1:
                    result = tokenizer.decode_ids(output_tokens[1:])
                    result = result.replace('<en2iu>', '').replace('<iu2en>', '').strip()
                    print(f"   Result: '{result}'")
                    
                    # Check if we got some output
                    if result and len(result) > 0:
                        success_count += 1
                        print(f"   ✅ Generated output")
                    else:
                        print(f"   ⚠️  Empty output")
                else:
                    print(f"   ⚠️  No tokens generated")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Test Results:")
    print(f"   - Successful translations: {success_count}/{len(test_sentences)}")
    print(f"   - Success rate: {success_count/len(test_sentences)*100:.1f}%")
    
    if success_count > 0:
        print("\n✅ Model is working! The neural translator is generating output.")
        print("💡 Note: Translation quality improves with more training data and epochs.")
        return True
    else:
        print("\n❌ Model is not generating output. May need retraining.")
        return False


def check_training_status():
    """Check the status of training."""
    
    print("\n📋 Training Status Check")
    print("-" * 30)
    
    # Check training info files
    info_files = [
        "nunavut_mt/models/improved_gpu/training_info.json",
        "nunavut_mt/models/efficient_gpu/training_info.json"
    ]
    
    for info_file in info_files:
        if Path(info_file).exists():
            try:
                import json
                with open(info_file, 'r') as f:
                    info = json.load(f)
                
                print(f"\n📄 {info_file}:")
                print(f"   - Training samples: {info.get('training_samples', 'N/A'):,}")
                print(f"   - Epochs: {info.get('epochs', 'N/A')}")
                print(f"   - Final loss: {info.get('final_loss', 'N/A'):.4f}")
                print(f"   - Device: {info.get('device', 'N/A')}")
                print(f"   - GPU: {info.get('gpu_name', 'N/A')}")
                print(f"   - Model params: {info.get('model_params', 'N/A'):,}")
                
            except Exception as e:
                print(f"   ❌ Error reading {info_file}: {e}")
    
    # Check model files
    model_files = [
        "nunavut_mt/models/improved_gpu/improved_translation_model.pt",
        "nunavut_mt/models/efficient_gpu/efficient_translation_model.pt"
    ]
    
    print(f"\n📁 Model Files:")
    for model_file in model_files:
        if Path(model_file).exists():
            size_mb = Path(model_file).stat().st_size / (1024 * 1024)
            print(f"   ✅ {model_file} ({size_mb:.1f} MB)")
        else:
            print(f"   ❌ {model_file} (missing)")


if __name__ == "__main__":
    print("🚀 Nunavut MT - Simple Test & Status Check")
    print("=" * 60)
    
    # Check training status
    check_training_status()
    
    # Test the model
    success = simple_test()
    
    if success:
        print("\n🎉 Everything looks good!")
        print("\n💡 Next steps:")
        print("   - Run: python nunavut_mt/test_neural_model.py")
        print("   - Or use: python nunavut_mt/translate_cli.py --text 'Hello' --direction en2iu")
    else:
        print("\n🔧 Troubleshooting needed.")
        print("   - Try rerunning training: python nunavut_mt/train_efficient_gpu.py")
