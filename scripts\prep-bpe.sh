#!/bin/bash

# @file prep-bpe.sh 
# @brief Full BPE pipeline using subword-nmt.
# 
# <AUTHOR>
# 
# Traitement multilingue de textes / Multilingual Text Processing
# Centre de recherche en technologies numériques / Digital Technologies Research Centre
# Conseil national de recherches Canada / National Research Council Canada
# Copyright 2020, Sa Majeste la Reine du Chef du Canada /
# Copyright 2020, Her Majesty in Right of Canada

default_data_dir=../preprocessing/tc

usage() {
   for msg in "$@"; do
      echo $msg >&2
   done
   [[ $0 =~ [^/]*$ ]] && PROG=$BASH_REMATCH || PROG=$0
   cat <<==EOF== >&2

Usage: prep-bpe.sh [options] <bpe-size>

  Train either separate BPE models or a joint model for the src and tgt languages,
  and apply the model to the training files.

Options:

  -j(oint)    train a joint BPE model. [no]
  -dd|-datadir PATH
              use PATH to locate the training files. [$default_data_dir]
  -vt|-vocab-threshold POS_INT 
              use a vocabulary threshold of POS_INT. [no threshold]
  -h(elp)     print this help message
  -v(erbose)  increment the verbosity level by 1 (may be repeated)
  -d(ebug)    print debugging information

==EOF==

   exit 1
}

error_exit() {
   echo -n "prep-bpe.sh fatal error: " >&2
   for msg in "$@"; do
      echo $msg >&2
   done
   echo "Use -h for help." >&2
   exit 1
}

# arg_check_pos_int $value $arg_name exits with an error if $value does not
# represent a positive integer, using $arg_name to provide a meaningful error
# message.
arg_check_pos_int() {
#   expr $1 + 0 &> /dev/null
   [ "$1" -eq "$1" ] 2> /dev/null
   RC=$?
#   if [[ $1 =~ ^[0-9]*$ ]]; then RC=0; else RC=2; fi
   if [ $RC != 0 -a $RC != 1 ] || [ $1 -le 0 ]; then
      error_exit "Invalid argument to $2 option: $1; positive integer expected."
   fi
}

arg_check() {
   if [ $2 -le $1 ]; then
      error_exit "Missing argument to $3 option."
   fi
}

# arg_check_int $value $arg_name exits with an error if $value does not
# represent an integer, using $arg_name to provide a meaningful error message.
arg_check_int() {
   expr $1 + 0 &> /dev/null
   RC=$?
   if [ $RC != 0 -a $RC != 1 ]; then
      error_exit "Invalid argument to $2 option: $1; integer expected."
   fi
}

# Command line processing [Remove irrelevant parts of this code when you use
# this template]
VERBOSE=0
joint_bpe=
data_dir=$default_data_dir
vocab_threshold=
while [ $# -gt 0 ]; do
   case "$1" in
      -j|-joint)            joint_bpe=1;;
      -dd|-data-dir)        arg_check 1 $# $1; data_dir=$2; shift;;
      -vt|-vocab-threshold) arg_check 1 $# $1; arg_check_pos_int $2 $1
                            vocab_threshold=$2; shift;;
      -v|-verbose)          VERBOSE=$(( $VERBOSE + 1 ));;
      -d|-debug)            DEBUG=1;;
      -h|-help)             usage;;
      --)                   shift; break;;
      -*)                   error_exit "Unknown option $1.";;
      *)                    break;;
   esac
   shift
done

[[ $# -eq 0 ]]  && error_exit "Missing required argument: bpe_size"
arg_check_pos_int $1 bpe_size
bpe_size=$1; shift
[[ $# -gt 0 ]]  && error_exit "Superfluous arguments: $*"

# Don't set errexit until after arguments have been checked.
set -o errexit; set -o pipefail

vocab_threshold_arg=
if [[ $vocab_threshold ]]; then
   vocab_threshold_arg="--vocabulary-threshold $vocab_threshold"
fi

readonly srcl=en
readonly tgtl=iu

# prepare the bpe_dir name
bpe_dir=bpe
if [[ ${data_dir##*/} != ${default_data_dir##*/} ]]; then
   dd_tag=${data_dir##*/}
   dd_tag=${dd_tag#${default_data_dir##*/}.}
   bpe_dir=$dd_tag.bpe
fi
if [[ $joint_bpe ]]; then
   bpe_dir+=.joint
else
   bpe_dir+=.sep
fi
if [[ $bpe_size -lt 1000 ]]; then
   bpe_dir+=.$bpe_size
elif [[ $bpe_size -lt 10000 ]]; then
   bpe_dir+=.0$((bpe_size / 1000))k
else
   bpe_dir+=.$((bpe_size / 1000))k
fi
if [[ $vocab_threshold ]]; then
   no_vocthr_dir=$bpe_dir
   bpe_dir+=.vt$vocab_threshold
fi

echo Creating $bpe_dir >&2

mkdir -p $bpe_dir
mkdir -p $bpe_dir/logs
mkdir -p $bpe_dir/model

log_file=$bpe_dir/logs/log.bpe

# backup any existing log file
if [[ -s $log_file ]]; then
   cp -p -f --backup=numbered $log_file $log_file
fi

cat /dev/null > $log_file

if [[ $joint_bpe ]]; then
   langs="$srcl$tgtl"
else
   langs="$srcl $tgtl"
fi

declare -A codes_files
declare -A new_model

for lang in $langs; do
   codes_file=$bpe_dir/model/bpe.$lang.$bpe_size.codes
   if [[ $lang = $srcl$tgtl ]]; then
      data_files="$data_dir/train.$srcl $data_dir/train.$tgtl"
      codes_files[$srcl]=$codes_file
      codes_files[$tgtl]=$codes_file
   else
      data_files=$data_dir/train.$lang
      codes_files[$lang]=$codes_file
   fi
   
   # Generate the codes file.
   if [[ -s $codes_file ]]; then
      echo "[$(date)] Codes file $codes_file already exists." >&2
   elif [[ $no_vocthr_dir && -s $no_vocthr_dir/${codes_file##*/} ]]; then
      echo "[$(date)] Symlinking to codes file $no_vocthr_dir/model/${codes_file##*/}." >&2
      ln -sf ../../$no_vocthr_dir/model/${codes_file##*/} $codes_file
      new_model[$lang]=sym
   else
      echo "[$(date)] Learning BPE codes $codes_file of size $bpe_size on $data_files." >&2   
      subword-nmt learn-bpe -s $bpe_size --total-symbols -i <(cat $data_files) \
         > $codes_file
      new_model[$lang]=yes
      chmod a-w $codes_file
   fi
done 2>> $log_file
   
for lang in $srcl $tgtl; do
   # Generate the corresponding vocab files.
   if [[ ${new_model[$lang]} != yes && -s $bpe_dir/model/vocab.$lang ]]; then
      echo "[$(date)] Vocab file $bpe_dir/model/vocab.$lang already exists." >&2
   elif [[ ${new_model[$lang]} = sym && -s $no_vocthr_dir/model/vocab.$lang ]]; then
      echo "[$(date)] Symlinking to vocab file $no_vocthr_dir/model/vocab.$lang." >&2
      ln -sf ../../$no_vocthr_dir/model/vocab.$lang $bpe_dir/model/vocab.$lang
   else
      echo "[$(date)] Extracting BPE vocab $bpe_dir/model/vocab.$lang for codes file " \
           "${codes_files[$lang]} and $data_dir/train.$lang" >&2
      subword-nmt apply-bpe -c ${codes_files[$lang]} < $data_dir/train.$lang \
         | subword-nmt get-vocab > $bpe_dir/model/vocab.$lang
      new_model[$lang]=yes
      chmod a-w $bpe_dir/model/vocab.$lang
   fi
done 2>> $log_file

# Apply the BPE model(s) to the training data.
for stem in train dev-dedup devtest-dedup test-dedup; do
   for lang in $srcl $tgtl; do
      if [[ ! ${new_model[$lang]} && ! ${new_model[$srcl$tgtl]} && -s $bpe_dir/$stem.$lang ]]; then
         echo "[$(date)] BPE file $bpe_dir/$stem.$lang already exists." >&2
      else
         echo "[$(date)] Applying BPE codes ${codes_files[$lang]} and vocab $bpe_dir/model/vocab.$lang" \
              "$vocab_threshold_arg to $data_dir/$stem.$lang, generating $bpe_dir/$stem.$lang" >&2
         subword-nmt apply-bpe -c ${codes_files[$lang]} --vocabulary $bpe_dir/model/vocab.$lang \
            $vocab_threshold_arg < $data_dir/$stem.$lang \
            > $bpe_dir/$stem.$lang 2>&2
      fi
   done
done 2>> $log_file

echo "[$(date)] Done." >> $log_file
