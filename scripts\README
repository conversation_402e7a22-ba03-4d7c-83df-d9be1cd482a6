                   Nunavut Hansard 3.0 processing scripts

Scripts to allow other research groups to reproduce the preprocessing used for
the NRC's baseline Machine Translation experiments.

Traitement multilingue de textes / Multilingual Text Processing
Centre de recherche en technologies numériques / Digital Technologies Research Centre
Conseil national de recherches Canada / National Research Council Canada
Copyright 2020, Sa Majeste la Reine du Chef du Canada /
Copyright 2020, Her Majesty in Right of Canada


preprocess-romanized.sh: Run our preprocessing for English and romanized Inuktitut (Hansard v3).
Before using, modify the paths documented at the head of the script.

normalize-apos-romanized.pl: Used by preprocess-romanized.sh to handle encoding issues and
apostrophes for romanized Inuktitut.

preprocess-syllabics.sh: Run our preprocessing for English and Inuktitut syllabics (Hansard v3).
Before using, modify the paths documented at the head of the script.

normalize-apos-syllabics.pl: Used by preprocess-syllabics.sh to handle quotation marks and
apostrophes for Inuktitut syllabics.

track_removal.py: Used by preprocess-romanized.sh and preprocess-syllabics.sh to get a list of
IDs of the remaining lines after cleaning.

All other scripts used in preprocess-romanized.sh and preprocess-syllabics.sh are from Moses.
(https://github.com/moses-smt/mosesdecoder git commit b1163966b1a9b4a3d6eec5a54b8bbf5f674a447b)

Assuming Moses is located in mosesscripts, to detokenize data, run:
mosesscripts/tokenizer/detokenizer.perl -l en < TOKENIZED_FILE > OUTPUT

prep-bpe.sh: Run the subword-nmt BPE processing pipeline.
Use prep-bpe.sh -h to get usage details.

We used version 0.3.6 of subword-nmt installed via pip:
pip3 install subword-nmt=0.3.6
(https://github.com/rsennrich/subword-nmt.git git commit fabe72d4f6c652aecbe20a77eb61e799eacb0731)

To run the romanized preprocessing pipeline, edit preprocess-romanized.sh to modify the paths,
and then run preprocess-romanized.sh followed by prep-bpe.sh.
For example:
   cd <path-to-scripts>
   # Edit preprocess-romanized.sh to modify paths.
   ./preprocess-romanized.sh
   # Assuming <preprocessing-dir> is the output directory for preprocess-romanized.sh, run
   # BPE processing with 5k merges, separate source and target BPE models.
   cd <preprocessing-dir>
   <path-to-scripts>/prep-bpe.sh -dd tc 5000
   # BPEed train/devtest files will be found in: <preprocessing-dir>/bpe.sep.05k/

To run the syllabics preprocessing pipeline, edit preprocess-syllabics.sh to modify the paths,
and then run preprocess-syllabics.sh followed by prep-bpe.sh, in the same manner as above.

normalize-iu-spelling.pl: perform basic spelling normalization on Inuktitut.
Recommended. See the normalize-iu-spelling.readme for details.
This script is not incorporated in the above pipeline because we did not use it
in our MT experiments yet, but it could be used on the original corpus files before
running the preprocessing scripts, or incorporated into the preprocessing scripts.
