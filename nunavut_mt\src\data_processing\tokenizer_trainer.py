"""
Tokenizer Training Module for Nunavut Hansard Corpus

This module trains a SentencePiece tokenizer on the combined English-Inuktitut corpus
with proper handling of Inuktitut syllabics.
"""

import os
import sentencepiece as spm
from pathlib import Path
from typing import List, Dict, Optional
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TokenizerTrainer:
    """Trainer for SentencePiece tokenizer on the parallel corpus."""
    
    def __init__(self, data_dir: str, output_dir: str):
        """
        Initialize the tokenizer trainer.
        
        Args:
            data_dir: Directory containing processed corpus data
            output_dir: Directory to save trained tokenizer
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def prepare_training_data(self) -> str:
        """
        Prepare training data for SentencePiece by combining all text.
        
        Returns:
            Path to the combined training file
        """
        training_file = self.output_dir / "tokenizer_training_data.txt"
        
        with open(training_file, 'w', encoding='utf-8') as fout:
            # Add English training data
            en_file = self.data_dir / "train.en"
            if en_file.exists():
                logger.info(f"Adding English data from {en_file}")
                with open(en_file, 'r', encoding='utf-8') as fin:
                    for line in fin:
                        line = line.strip()
                        if line:
                            fout.write(line + '\n')
            
            # Add Inuktitut training data
            iu_file = self.data_dir / "train.iu"
            if iu_file.exists():
                logger.info(f"Adding Inuktitut data from {iu_file}")
                with open(iu_file, 'r', encoding='utf-8') as fin:
                    for line in fin:
                        line = line.strip()
                        if line:
                            fout.write(line + '\n')
        
        logger.info(f"Training data prepared: {training_file}")
        return str(training_file)
    
    def train_tokenizer(self, 
                       vocab_size: int = 32000,
                       model_type: str = "bpe",
                       character_coverage: float = 0.9995,
                       input_sentence_size: int = 10000000,
                       shuffle_input_sentence: bool = True) -> str:
        """
        Train a SentencePiece tokenizer.
        
        Args:
            vocab_size: Target vocabulary size
            model_type: Model type ("bpe", "unigram", "char", "word")
            character_coverage: Character coverage for vocabulary
            input_sentence_size: Maximum number of sentences for training
            shuffle_input_sentence: Whether to shuffle input sentences
            
        Returns:
            Path to the trained model
        """
        # Prepare training data
        training_file = self.prepare_training_data()
        
        # Output model path
        model_prefix = self.output_dir / f"tokenizer_v{vocab_size}"
        
        # SentencePiece training arguments
        train_args = [
            f"--input={training_file}",
            f"--model_prefix={model_prefix}",
            f"--vocab_size={vocab_size}",
            f"--model_type={model_type}",
            f"--character_coverage={character_coverage}",
            f"--input_sentence_size={input_sentence_size}",
            f"--shuffle_input_sentence={shuffle_input_sentence}",
            "--normalization_rule_name=nfkc",  # Unicode normalization
            "--remove_extra_whitespaces=true",
            "--split_by_unicode_script=true",  # Important for mixed scripts
            "--split_by_whitespace=true",
            "--split_by_number=true",
            "--split_digits=true",
            "--treat_whitespace_as_suffix=false",
            "--allow_whitespace_only_pieces=true",
            "--byte_fallback=true",  # Handle unknown characters
            # Special tokens
            "--pad_id=0",
            "--unk_id=1", 
            "--bos_id=2",
            "--eos_id=3",
            "--user_defined_symbols=<en2iu>,<iu2en>",  # Language direction tokens
        ]
        
        logger.info(f"Training SentencePiece tokenizer with vocab_size={vocab_size}")
        logger.info(f"Training arguments: {' '.join(train_args)}")
        
        # Train the model
        spm.SentencePieceTrainer.train(' '.join(train_args))
        
        model_path = f"{model_prefix}.model"
        vocab_path = f"{model_prefix}.vocab"
        
        logger.info(f"Tokenizer training complete!")
        logger.info(f"Model saved to: {model_path}")
        logger.info(f"Vocabulary saved to: {vocab_path}")
        
        return model_path
    
    def test_tokenizer(self, model_path: str, test_sentences: Optional[List[str]] = None):
        """
        Test the trained tokenizer with sample sentences.
        
        Args:
            model_path: Path to the trained tokenizer model
            test_sentences: Optional list of test sentences
        """
        # Load the tokenizer
        sp = spm.SentencePieceProcessor()
        sp.load(model_path)
        
        # Default test sentences if none provided
        if test_sentences is None:
            test_sentences = [
                "Thank you, Mr. Speaker.",
                "ᖁᔭᓐᓇᒦᒃ, ᐃᒃᓯᕙᐅᑖᖅ.",
                "The Government of Nunavut is committed to improving education.",
                "ᓄᓇᕗᑦ ᒐᕙᒪᒃᑯᖏᑦ ᐃᓕᓐᓂᐊᖅᑐᓕᕆᓂᕐᒥᒃ ᐱᐅᓯᒋᐊᕈᒪᔪᑦ.",
                "<en2iu> Hello, how are you?",
                "<iu2en> ᐊᐃ, ᖃᓄᐃᓕᖓᕙ?"
            ]
        
        logger.info("Testing tokenizer with sample sentences:")
        logger.info("=" * 60)
        
        for sentence in test_sentences:
            # Tokenize
            tokens = sp.encode_as_pieces(sentence)
            ids = sp.encode_as_ids(sentence)
            
            # Detokenize
            reconstructed = sp.decode_pieces(tokens)
            
            logger.info(f"Original: {sentence}")
            logger.info(f"Tokens:   {tokens}")
            logger.info(f"IDs:      {ids[:10]}{'...' if len(ids) > 10 else ''}")
            logger.info(f"Reconstructed: {reconstructed}")
            logger.info("-" * 40)
    
    def get_tokenizer_stats(self, model_path: str) -> Dict:
        """
        Get statistics about the trained tokenizer.
        
        Args:
            model_path: Path to the trained tokenizer model
            
        Returns:
            Dictionary with tokenizer statistics
        """
        sp = spm.SentencePieceProcessor()
        sp.load(model_path)
        
        vocab_size = sp.get_piece_size()
        
        # Count different types of tokens
        syllabic_tokens = 0
        latin_tokens = 0
        special_tokens = 0
        
        for i in range(vocab_size):
            piece = sp.id_to_piece(i)
            if any('\u1400' <= c <= '\u167F' for c in piece):
                syllabic_tokens += 1
            elif any(c.isalpha() for c in piece):
                latin_tokens += 1
            elif piece.startswith('<') and piece.endswith('>'):
                special_tokens += 1
        
        stats = {
            'vocab_size': vocab_size,
            'syllabic_tokens': syllabic_tokens,
            'latin_tokens': latin_tokens,
            'special_tokens': special_tokens,
            'other_tokens': vocab_size - syllabic_tokens - latin_tokens - special_tokens,
            'model_path': model_path
        }
        
        return stats
    
    def save_tokenizer_config(self, model_path: str, config_path: Optional[str] = None):
        """
        Save tokenizer configuration for easy loading.
        
        Args:
            model_path: Path to the trained tokenizer model
            config_path: Optional path to save config (defaults to same dir as model)
        """
        if config_path is None:
            config_path = Path(model_path).parent / "tokenizer_config.json"
        
        stats = self.get_tokenizer_stats(model_path)
        
        config = {
            'model_path': model_path,
            'vocab_path': model_path.replace('.model', '.vocab'),
            'vocab_size': stats['vocab_size'],
            'special_tokens': {
                'pad_token': '<pad>',
                'unk_token': '<unk>',
                'bos_token': '<s>',
                'eos_token': '</s>',
                'en2iu_token': '<en2iu>',
                'iu2en_token': '<iu2en>'
            },
            'special_token_ids': {
                'pad_id': 0,
                'unk_id': 1,
                'bos_id': 2,
                'eos_id': 3
            },
            'stats': stats
        }
        
        import json
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Tokenizer config saved to: {config_path}")
        return config_path


def main():
    """Main function to train the tokenizer."""
    # Initialize trainer
    trainer = TokenizerTrainer(
        data_dir="nunavut_mt/data/processed",
        output_dir="nunavut_mt/models/tokenizer"
    )
    
    # Train tokenizer with different vocabulary sizes
    vocab_sizes = [16000, 32000]
    
    for vocab_size in vocab_sizes:
        logger.info(f"\n{'='*60}")
        logger.info(f"Training tokenizer with vocab_size={vocab_size}")
        logger.info(f"{'='*60}")
        
        # Train the tokenizer
        model_path = trainer.train_tokenizer(vocab_size=vocab_size)
        
        # Test the tokenizer
        trainer.test_tokenizer(model_path)
        
        # Get and print statistics
        stats = trainer.get_tokenizer_stats(model_path)
        logger.info(f"\nTokenizer Statistics:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # Save configuration
        trainer.save_tokenizer_config(model_path)
        
        logger.info(f"Tokenizer training complete for vocab_size={vocab_size}")


if __name__ == "__main__":
    main()
