"""
Evaluation metrics for machine translation.

This module provides various metrics for evaluating translation quality,
including BLEU, chrF, and other MT-specific metrics.
"""

import re
import math
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class BLEUScore:
    """BLEU score implementation."""
    
    def __init__(self, max_n: int = 4, smooth: bool = True):
        """
        Initialize BLEU scorer.
        
        Args:
            max_n: Maximum n-gram order
            smooth: Whether to apply smoothing for zero counts
        """
        self.max_n = max_n
        self.smooth = smooth
    
    def _get_ngrams(self, tokens: List[str], n: int) -> Counter:
        """Get n-grams from tokens."""
        if n > len(tokens):
            return Counter()
        
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngrams.append(tuple(tokens[i:i+n]))
        
        return Counter(ngrams)
    
    def _compute_precision(self, candidate: List[str], references: List[List[str]], n: int) -> Tuple[int, int]:
        """Compute n-gram precision."""
        candidate_ngrams = self._get_ngrams(candidate, n)
        
        if not candidate_ngrams:
            return 0, 0
        
        # Get maximum counts from all references
        max_ref_counts = Counter()
        for reference in references:
            ref_ngrams = self._get_ngrams(reference, n)
            for ngram in ref_ngrams:
                max_ref_counts[ngram] = max(max_ref_counts[ngram], ref_ngrams[ngram])
        
        # Count matches
        matches = 0
        total = 0
        
        for ngram, count in candidate_ngrams.items():
            matches += min(count, max_ref_counts[ngram])
            total += count
        
        return matches, total
    
    def _brevity_penalty(self, candidate_len: int, reference_lens: List[int]) -> float:
        """Compute brevity penalty."""
        # Find closest reference length
        closest_ref_len = min(reference_lens, key=lambda x: abs(x - candidate_len))
        
        if candidate_len > closest_ref_len:
            return 1.0
        else:
            return math.exp(1 - closest_ref_len / candidate_len) if candidate_len > 0 else 0.0
    
    def compute_bleu(self, candidate: str, references: List[str]) -> Dict[str, float]:
        """
        Compute BLEU score for a single candidate against multiple references.
        
        Args:
            candidate: Candidate translation
            references: List of reference translations
            
        Returns:
            Dictionary with BLEU scores and components
        """
        # Tokenize
        candidate_tokens = candidate.lower().split()
        reference_tokens = [ref.lower().split() for ref in references]
        
        if not candidate_tokens:
            return {'bleu': 0.0, 'precisions': [0.0] * self.max_n, 'bp': 0.0, 'length_ratio': 0.0}
        
        # Compute n-gram precisions
        precisions = []
        for n in range(1, self.max_n + 1):
            matches, total = self._compute_precision(candidate_tokens, reference_tokens, n)
            
            if total == 0:
                precision = 0.0
            elif matches == 0 and self.smooth:
                precision = 1.0 / total  # Add-one smoothing
            else:
                precision = matches / total
            
            precisions.append(precision)
        
        # Compute brevity penalty
        candidate_len = len(candidate_tokens)
        reference_lens = [len(ref) for ref in reference_tokens]
        bp = self._brevity_penalty(candidate_len, reference_lens)
        
        # Compute BLEU score
        if all(p > 0 for p in precisions):
            log_precisions = [math.log(p) for p in precisions]
            bleu = bp * math.exp(sum(log_precisions) / len(log_precisions))
        else:
            bleu = 0.0
        
        return {
            'bleu': bleu,
            'precisions': precisions,
            'bp': bp,
            'length_ratio': candidate_len / (sum(reference_lens) / len(reference_lens)) if reference_lens else 0.0
        }
    
    def corpus_bleu(self, candidates: List[str], references: List[List[str]]) -> Dict[str, float]:
        """
        Compute corpus-level BLEU score.
        
        Args:
            candidates: List of candidate translations
            references: List of reference lists (each candidate can have multiple references)
            
        Returns:
            Dictionary with corpus BLEU score and components
        """
        assert len(candidates) == len(references), "Number of candidates and references must match"
        
        total_matches = [0] * self.max_n
        total_counts = [0] * self.max_n
        total_candidate_len = 0
        total_reference_len = 0
        
        for candidate, refs in zip(candidates, references):
            candidate_tokens = candidate.lower().split()
            reference_tokens = [ref.lower().split() for ref in refs]
            
            total_candidate_len += len(candidate_tokens)
            
            # Find closest reference length
            if reference_tokens:
                closest_ref_len = min([len(ref) for ref in reference_tokens], 
                                    key=lambda x: abs(x - len(candidate_tokens)))
                total_reference_len += closest_ref_len
            
            # Accumulate n-gram statistics
            for n in range(1, self.max_n + 1):
                matches, total = self._compute_precision(candidate_tokens, reference_tokens, n)
                total_matches[n-1] += matches
                total_counts[n-1] += total
        
        # Compute corpus-level precisions
        precisions = []
        for n in range(self.max_n):
            if total_counts[n] == 0:
                precision = 0.0
            elif total_matches[n] == 0 and self.smooth:
                precision = 1.0 / total_counts[n]
            else:
                precision = total_matches[n] / total_counts[n]
            precisions.append(precision)
        
        # Compute brevity penalty
        if total_candidate_len > total_reference_len:
            bp = 1.0
        else:
            bp = math.exp(1 - total_reference_len / total_candidate_len) if total_candidate_len > 0 else 0.0
        
        # Compute BLEU score
        if all(p > 0 for p in precisions):
            log_precisions = [math.log(p) for p in precisions]
            bleu = bp * math.exp(sum(log_precisions) / len(log_precisions))
        else:
            bleu = 0.0
        
        return {
            'bleu': bleu,
            'precisions': precisions,
            'bp': bp,
            'length_ratio': total_candidate_len / total_reference_len if total_reference_len > 0 else 0.0
        }


class ChrFScore:
    """Character-level F-score (chrF) implementation."""
    
    def __init__(self, n: int = 6, beta: float = 2.0, remove_whitespace: bool = True):
        """
        Initialize chrF scorer.
        
        Args:
            n: Maximum character n-gram order
            beta: Beta parameter for F-score (higher values favor recall)
            remove_whitespace: Whether to remove whitespace before scoring
        """
        self.n = n
        self.beta = beta
        self.remove_whitespace = remove_whitespace
    
    def _get_char_ngrams(self, text: str, n: int) -> Counter:
        """Get character n-grams from text."""
        if self.remove_whitespace:
            text = re.sub(r'\s+', '', text)
        
        if n > len(text):
            return Counter()
        
        ngrams = []
        for i in range(len(text) - n + 1):
            ngrams.append(text[i:i+n])
        
        return Counter(ngrams)
    
    def compute_chrf(self, candidate: str, references: List[str]) -> Dict[str, float]:
        """
        Compute chrF score for a single candidate against multiple references.
        
        Args:
            candidate: Candidate translation
            references: List of reference translations
            
        Returns:
            Dictionary with chrF score and components
        """
        if not candidate.strip():
            return {'chrf': 0.0, 'precision': 0.0, 'recall': 0.0, 'f_score': 0.0}
        
        total_precision = 0.0
        total_recall = 0.0
        
        # Compute precision and recall for each n-gram order
        for n in range(1, self.n + 1):
            candidate_ngrams = self._get_char_ngrams(candidate, n)
            
            if not candidate_ngrams:
                continue
            
            # Find best matching reference
            best_precision = 0.0
            best_recall = 0.0
            
            for reference in references:
                reference_ngrams = self._get_char_ngrams(reference, n)
                
                if not reference_ngrams:
                    continue
                
                # Compute matches
                matches = sum((candidate_ngrams & reference_ngrams).values())
                
                precision = matches / sum(candidate_ngrams.values()) if candidate_ngrams else 0.0
                recall = matches / sum(reference_ngrams.values()) if reference_ngrams else 0.0
                
                if precision + recall > best_precision + best_recall:
                    best_precision = precision
                    best_recall = recall
            
            total_precision += best_precision
            total_recall += best_recall
        
        # Average over n-gram orders
        avg_precision = total_precision / self.n
        avg_recall = total_recall / self.n
        
        # Compute F-score
        if avg_precision + avg_recall > 0:
            f_score = (1 + self.beta**2) * avg_precision * avg_recall / (self.beta**2 * avg_precision + avg_recall)
        else:
            f_score = 0.0
        
        return {
            'chrf': f_score,
            'precision': avg_precision,
            'recall': avg_recall,
            'f_score': f_score
        }
    
    def corpus_chrf(self, candidates: List[str], references: List[List[str]]) -> Dict[str, float]:
        """
        Compute corpus-level chrF score.
        
        Args:
            candidates: List of candidate translations
            references: List of reference lists
            
        Returns:
            Dictionary with corpus chrF score and components
        """
        assert len(candidates) == len(references), "Number of candidates and references must match"
        
        total_precision = 0.0
        total_recall = 0.0
        
        for candidate, refs in zip(candidates, references):
            score = self.compute_chrf(candidate, refs)
            total_precision += score['precision']
            total_recall += score['recall']
        
        # Average over corpus
        avg_precision = total_precision / len(candidates) if candidates else 0.0
        avg_recall = total_recall / len(candidates) if candidates else 0.0
        
        # Compute F-score
        if avg_precision + avg_recall > 0:
            f_score = (1 + self.beta**2) * avg_precision * avg_recall / (self.beta**2 * avg_precision + avg_recall)
        else:
            f_score = 0.0
        
        return {
            'chrf': f_score,
            'precision': avg_precision,
            'recall': avg_recall,
            'f_score': f_score
        }


class MTEvaluator:
    """Comprehensive MT evaluator combining multiple metrics."""
    
    def __init__(self):
        """Initialize evaluator with multiple metrics."""
        self.bleu_scorer = BLEUScore()
        self.chrf_scorer = ChrFScore()
    
    def evaluate_single(self, candidate: str, references: List[str]) -> Dict[str, float]:
        """
        Evaluate a single translation with multiple metrics.
        
        Args:
            candidate: Candidate translation
            references: List of reference translations
            
        Returns:
            Dictionary with all metric scores
        """
        bleu_scores = self.bleu_scorer.compute_bleu(candidate, references)
        chrf_scores = self.chrf_scorer.compute_chrf(candidate, references)
        
        return {
            'bleu': bleu_scores['bleu'],
            'bleu_1': bleu_scores['precisions'][0] if len(bleu_scores['precisions']) > 0 else 0.0,
            'bleu_2': bleu_scores['precisions'][1] if len(bleu_scores['precisions']) > 1 else 0.0,
            'bleu_3': bleu_scores['precisions'][2] if len(bleu_scores['precisions']) > 2 else 0.0,
            'bleu_4': bleu_scores['precisions'][3] if len(bleu_scores['precisions']) > 3 else 0.0,
            'chrf': chrf_scores['chrf'],
            'chrf_precision': chrf_scores['precision'],
            'chrf_recall': chrf_scores['recall'],
            'length_ratio': bleu_scores['length_ratio']
        }
    
    def evaluate_corpus(self, candidates: List[str], references: List[List[str]]) -> Dict[str, float]:
        """
        Evaluate corpus with multiple metrics.
        
        Args:
            candidates: List of candidate translations
            references: List of reference lists
            
        Returns:
            Dictionary with corpus-level metric scores
        """
        bleu_scores = self.bleu_scorer.corpus_bleu(candidates, references)
        chrf_scores = self.chrf_scorer.corpus_chrf(candidates, references)
        
        return {
            'corpus_bleu': bleu_scores['bleu'],
            'corpus_bleu_1': bleu_scores['precisions'][0] if len(bleu_scores['precisions']) > 0 else 0.0,
            'corpus_bleu_2': bleu_scores['precisions'][1] if len(bleu_scores['precisions']) > 1 else 0.0,
            'corpus_bleu_3': bleu_scores['precisions'][2] if len(bleu_scores['precisions']) > 2 else 0.0,
            'corpus_bleu_4': bleu_scores['precisions'][3] if len(bleu_scores['precisions']) > 3 else 0.0,
            'corpus_chrf': chrf_scores['chrf'],
            'corpus_chrf_precision': chrf_scores['precision'],
            'corpus_chrf_recall': chrf_scores['recall'],
            'corpus_length_ratio': bleu_scores['length_ratio']
        }


if __name__ == "__main__":
    # Example usage
    evaluator = MTEvaluator()
    
    # Test single sentence
    candidate = "Thank you, Mr. Speaker."
    references = ["Thank you, Mr. Speaker.", "Thanks, Mr. Speaker."]
    
    scores = evaluator.evaluate_single(candidate, references)
    print("Single sentence scores:")
    for metric, score in scores.items():
        print(f"  {metric}: {score:.4f}")
    
    # Test corpus
    candidates = ["Thank you, Mr. Speaker.", "The government is working hard."]
    references = [["Thank you, Mr. Speaker."], ["The government is working hard."]]
    
    corpus_scores = evaluator.evaluate_corpus(candidates, references)
    print("\nCorpus scores:")
    for metric, score in corpus_scores.items():
        print(f"  {metric}: {score:.4f}")
    
    print("\nEvaluation metrics implemented successfully!")
