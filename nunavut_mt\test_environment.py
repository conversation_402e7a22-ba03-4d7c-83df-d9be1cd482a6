#!/usr/bin/env python3
"""
Environment test script for Nunavut MT system.

This script tests if all required dependencies are properly installed
and the system is ready for training and inference.
"""

import sys
import importlib
import subprocess
from pathlib import Path

def test_import(module_name, package_name=None):
    """Test if a module can be imported."""
    try:
        module = importlib.import_module(module_name)
        version = getattr(module, '__version__', 'unknown')
        print(f"✅ {package_name or module_name}: {version}")
        return True, module
    except ImportError as e:
        print(f"❌ {package_name or module_name}: Not installed ({e})")
        return False, None

def test_python_version():
    """Test Python version."""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is compatible")
        return True
    else:
        print("❌ Python 3.8+ required")
        return False

def test_system_resources():
    """Test system resources."""
    try:
        import psutil
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('.')
        
        print(f"💾 RAM: {memory.total / (1024**3):.1f} GB total, {memory.available / (1024**3):.1f} GB available")
        print(f"💿 Disk: {disk.free / (1024**3):.1f} GB free")
        
        if memory.available > 4 * (1024**3):  # 4GB
            print("✅ Sufficient RAM available")
        else:
            print("⚠️  Low RAM - may affect training performance")
            
        if disk.free > 5 * (1024**3):  # 5GB
            print("✅ Sufficient disk space")
        else:
            print("⚠️  Low disk space")
            
    except ImportError:
        print("ℹ️  psutil not available - skipping resource check")

def test_pytorch():
    """Test PyTorch installation and capabilities."""
    success, torch = test_import('torch', 'PyTorch')
    if not success:
        return False
    
    # Test CUDA availability
    if torch.cuda.is_available():
        print(f"🚀 CUDA available: {torch.cuda.device_count()} device(s)")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("💻 CUDA not available - will use CPU")
    
    # Test basic tensor operations
    try:
        x = torch.randn(2, 3)
        y = torch.randn(3, 2)
        z = torch.mm(x, y)
        print("✅ PyTorch tensor operations working")
        return True
    except Exception as e:
        print(f"❌ PyTorch tensor operations failed: {e}")
        return False

def test_transformers():
    """Test Transformers library."""
    success, transformers = test_import('transformers', 'Transformers')
    if not success:
        return False
    
    try:
        # Test basic tokenizer
        from transformers import AutoTokenizer
        print("✅ Transformers AutoTokenizer available")
        return True
    except Exception as e:
        print(f"❌ Transformers test failed: {e}")
        return False

def test_sentencepiece():
    """Test SentencePiece."""
    success, spm = test_import('sentencepiece', 'SentencePiece')
    if not success:
        return False
    
    try:
        # Test basic SentencePiece functionality
        sp = spm.SentencePieceProcessor()
        print("✅ SentencePiece processor available")
        return True
    except Exception as e:
        print(f"❌ SentencePiece test failed: {e}")
        return False

def test_project_files():
    """Test if project files are in place."""
    required_files = [
        'nunavut_mt/src/models/bidirectional_mt.py',
        'nunavut_mt/src/inference/translator.py',
        'nunavut_mt/translate_cli.py',
        'nunavut_mt/models/tokenizer/tokenizer_v16000.model',
        'nunavut_mt/data/processed/train.en',
        'nunavut_mt/data/processed/train.iu'
    ]
    
    print("\n📁 Checking project files:")
    all_present = True
    
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            if path.is_file():
                size = path.stat().st_size
                print(f"✅ {file_path} ({size:,} bytes)")
            else:
                print(f"✅ {file_path} (directory)")
        else:
            print(f"❌ {file_path} - Missing")
            all_present = False
    
    return all_present

def test_tokenizer():
    """Test the trained tokenizer."""
    tokenizer_path = Path('nunavut_mt/models/tokenizer/tokenizer_v16000.model')
    
    if not tokenizer_path.exists():
        print("❌ Tokenizer model not found")
        return False
    
    try:
        import sentencepiece as spm
        sp = spm.SentencePieceProcessor()
        sp.load(str(tokenizer_path))
        
        # Test tokenization
        test_text = "Thank you, Mr. Speaker."
        tokens = sp.encode_as_pieces(test_text)
        
        print(f"✅ Tokenizer working: '{test_text}' -> {len(tokens)} tokens")
        return True
    except Exception as e:
        print(f"❌ Tokenizer test failed: {e}")
        return False

def test_translation_system():
    """Test the translation system."""
    try:
        # Test CLI import
        sys.path.append('nunavut_mt/src')
        from inference.translator import TranslationPipeline
        
        # Initialize pipeline
        pipeline = TranslationPipeline('nunavut_mt/models/tokenizer/tokenizer_v16000.model')
        
        # Test translation
        result = pipeline.translate_text("Hello", direction="en2iu")
        
        print(f"✅ Translation system working: 'Hello' -> '{result['translation']}'")
        return True
    except Exception as e:
        print(f"❌ Translation system test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("🧪 Nunavut MT Environment Test")
    print("=" * 60)
    
    tests = [
        ("Python Version", test_python_version),
        ("System Resources", test_system_resources),
        ("PyTorch", test_pytorch),
        ("Transformers", test_transformers),
        ("SentencePiece", test_sentencepiece),
        ("Project Files", test_project_files),
        ("Tokenizer", test_tokenizer),
        ("Translation System", test_translation_system),
    ]
    
    # Test core dependencies
    print("\n📦 Testing core dependencies:")
    core_deps = [
        'numpy', 'pandas', 'tqdm', 'pyyaml', 'scikit-learn',
        'matplotlib', 'seaborn'
    ]
    
    for dep in core_deps:
        test_import(dep)
    
    # Run main tests
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your environment is ready.")
        print("\nNext steps:")
        print("1. Train the model: python nunavut_mt/train_model.py")
        print("2. Test translation: python nunavut_mt/translate_cli.py --text 'Hello' --direction en2iu")
    else:
        print("⚠️  Some tests failed. Please check the setup guide.")
        print("Run: python nunavut_mt/setup_env.py (or .bat/.sh)")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
