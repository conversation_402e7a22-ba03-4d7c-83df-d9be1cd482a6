name: nunavut-mt
channels:
  - pytorch
  - huggingface
  - conda-forge
  - defaults
dependencies:
  - python=3.9
  - pip
  - numpy=1.24.3
  - scipy=1.10.1
  - pandas=2.0.3
  - matplotlib=3.7.1
  - seaborn=0.12.2
  - tqdm=4.65.0
  - pyyaml=6.0
  - scikit-learn=1.3.0
  - jupyter
  - ipykernel
  - pip:
    - torch==2.0.1
    - torchvision==0.15.2
    - torchaudio==2.0.2
    - transformers==4.30.2
    - datasets==2.12.0
    - tokenizers==0.13.3
    - sentencepiece==0.1.99
    - sacrebleu==2.3.1
    - evaluate==0.4.0
    - accelerate==0.20.3
    - wandb==0.15.4
    - tensorboard==2.13.0
    - protobuf==3.20.3
    - huggingface-hub==0.15.1
    - safetensors==0.3.1
    - regex==2023.6.3
    - requests==2.31.0
    - filelock==3.12.2
    - packaging==23.1
