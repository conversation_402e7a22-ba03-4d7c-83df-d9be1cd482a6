#!/usr/bin/env python3
"""
Web scraper to build additional dictionary from online sources.
"""

import re
import json
import time
from pathlib import Path
import logging
from collections import defaultdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_tusaalanga_glossary():
    """Extract vocabulary from the Tusaalanga glossary we fetched."""
    
    # This is the content we already fetched
    glossary_content = """
aagga ᐋᒡᒐ no
aaggaa suli ᐋᒡᒑ ᓱᓕ not yet
aaggaqai ᐋᒡᒐᖃᐃ probably not; I don't think so.
aaggiisi ᐋᒡᒌᓯ August
aanniajuq ᐋᓐᓂᐊᔪᖅ sick; in pain (he/she is...)
aanniajuq ᐋᓐᓂᐊᔪᖅ pain (he/she is in...)
aann<PERSON>iuqti ᐋᓐᓂᐊᓯᐅᖅᑎ nurse
aann<PERSON>iuqtiujunga ᐋᓐᓂᐊᓯᐅᖅᑎᐅᔪᖓ nurse (I am a...)
aanniavik ᐋᓐᓂᐊᕕᒃ hospital
aanniqtuq ᐋᓐᓂᖅᑐᖅ injured; hurt (he/she is...)
Aantiuriu ᐋᓐᑎᐅᕆᐅ Ontario
aarluk ᐋᕐᓗᒃ orca
aatsuu ᐋᑦᓲ I don't know.
Aatuvaa ᐋᑐᕚ Ottawa
Aatuvaamiutaujunga ᐋᑐᕚᒥᐅᑕᐅᔪᖓ I'm from Ottawa.
Aavuuta ᐋᕘᑕ Alberta
aggaak ᐊᒡᒑᒃ gloves (two)
aggat ᐊᒡᒐᑦ hand
aggaut ᐊᒡᒐᐅᑦ forearm
aggualaqisaaq ᐊᒡᒍᐊᓚᕿᓵᖅ stew
ailiruk! ᐊᐃᓕᕈᒃ! Go get it! (command)
aippaq ᐊᐃᑉᐸᖅ spouse; partner; common-law
aippikkut ᐊᐃᑉᐱᒃᑯᑦ Tuesdays (on...)
aippiq ᐊᐃᑉᐱᖅ Tuesday
aippitamaat ᐊᐃᑉᐱᑕᒫᑦ Tuesday (every...)
airaapik ᐊᐃᕌᐱᒃ brother-in-law (sister's husband)
airaapik ᐊᐃᕌᐱᒃ sister-in-law (brother's wife)
aitsiqtuq ᐊᐃᑦᓯᖅᑐᖅ get someone/something (he/she goes to...)
aitsirniaqtunga ᐊᐃᑦᓯᕐᓂᐊᖅᑐᖓ get someone (I will go...)
Aivilik ᐊᐃᕕᓕᒃ coast between Chesterfield Inlet and Repulse Bay
aiviq ᐊᐃᕕᖅ walrus
aivvaktuq ᐊᐃᕝᕙᒃᑐᖅ walrus (he/she caught a ...)
aivviaqtuq ᐊᐃᕝᕕᐊᖅᑐᖅ walrus hunting (he/she has gone...)
ajak ᐊᔭᒃ aunt (mother's sister)
ajjigiik ᐊᔾᔨᒌᒃ same (they are the...)
ajjigiingittuuk ᐊᔾᔨᒌᖏᑦᑑᒃ different (the two of them are...)
ajjiliurut ᐊᔾᔨᓕᐅᕈᑦ camera
ajjinnguaq ᐊᔾᔨᙳᐊᖅ photograph
ajuqsangittuq ᐊᔪᖅᓴᖏᑦᑐᖅ rich; he/she has plenty 
ajuqsaqtuq ᐊᔪᖅᓴᖅᑐᖅ poor; he/she is in need
akaugunniiqtuq ᐊᑲᐅᒍᓐᓃᖅᑐᖅ turned bad; deteriorated
akaujuq ᐊᑲᐅᔪᖅ good; convenient
akautsianngittunga ᐊᑲᐅᑦᓯᐊᙱᑦᑐᖓ I don't feel well.
aki ᐊᑭ price
akia ᐊᑭᐊ the other side
akiani ᐊᑭᐊᓂ across from; on the other side
akikilligiaqsimajuq ᐊᑭᑭᓪᓕᒋᐊᖅᓯᒪᔪᖅ on sale
akikittuq ᐊᑭᑭᑦᑐᖅ cheap (it is...)
akilik ᐊᑭᓕᒃ costs something; it is not free.
akilitsaq ᐊᑭᓕᑦᓴᖅ bill; receipt
akiqalauqtuq ᐊᑭᖃᓚᐅᖅᑐᖅ cost (it...$1,200 dollars)
akiqanngittuq ᐊᑭᖃᙱᑦᑐᖅ free; it has no cost
akisiq ᐊᑭᓯᖅ pillow
akitujuq ᐊᑭᑐᔪᖅ expensive (it is...)
akkak ᐊᒃᑲᒃ uncle (father's brother)
akpa ᐊᒃᐸ murre
Akukittut ᐊᑯᑭᑦᑐᑦ Greenland (informal)
akuliaq ᐊᑯᓕᐊᖅ area between the eyes
akuni ᐊᑯᓂ distance between objects; time between events
akunialuk ᐊᑯᓂᐊᓗᒃ long time (a...)
akunigusuttuq ᐊᑯᓂᒍᓱᑦᑐᖅ impatient (he/she feels...)
akuniujaqtulaarama ᐊᑯᓂᐅᔭᖅᑐᓛᕋᒪ long time (I will be going for a...)
akunninganiittuq ᐊᑯᓐᓂᖓᓃᑦᑐᖅ between (it is...)
akuq ᐊᑯᖅ amaut (long tailed style)
akłak ᐊᒃᖤᒃ grizzly bear
alianaigijara ᐊᓕᐊᓇᐃᒋᔭᕋ joy (it gives me...); I enjoy it.
alianaigusuttuq ᐊᓕᐊᓇᐃᒍᓱᑦᑐᖅ enjoys something; he/she is having fun doing something 
alianait ᐊᓕᐊᓇᐃᑦ wonderful (it's...)
aliqsiik ᐊᓕᖅᓰᒃ socks (a pair)
allarut ᐊᓪᓚᕈᑦ towel
allavvik ᐊᓪᓚᕝᕕᒃ office
allavvimmi ᐊᓪᓚᕝᕕᒻᒥ office (in/at the...)
allavvimmit ᐊᓪᓚᕝᕕᒻᒥᑦ from the office
allavvimmut ᐊᓪᓚᕝᕕᒻᒧᑦ to the office
alliruujaak ᐊᓪᓕᕉᔮᒃ scissors
alliruuk ᐊᓪᓕᕉᒃ jaws (2)
allu ᐊᓪᓗ hole in the ice where seals come up to breathe
allusiuqtuq ᐊᓪᓗᓯᐅᖅᑐᖅ seals' breathing holes (he/she looks for...)
aluiqqanaq ᐊᓗᐃᖅᑲᓇᖅ avalanche
alurluujaut ᐊᓗᕐᓘᔭᐅᑦ rug
aluut ᐊᓘᑦ spoon
aluutiqpak ᐊᓘᑎᖅᐸᒃ spoon (big)
amaruq ᐊᒪᕈᖅ wolf
amauq ᐊᒪᐅᖅ great-grandfather
amauqpaaq ᐊᒪᐅᖅᐹᖅ great-great grandfather
amauti ᐊᒪᐅᑎ parka with pouch at the back for carrying a child
amiat ᐊᒥᐊᑦ colours
amiq ᐊᒥᖅ skin (land animals)
amisuliurut ᐊᒥᓱᓕᐅᕈᑦ photocopier
amisut ᐊᒥᓱᑦ lots, many
amittuq ᐊᒥᑦᑐᖅ narrow (it is...)
ammalu ᐊᒻᒪᓗ and
ammattauq ᐊᒻᒪᑦᑕᐅᖅ and; what's more
ammut ᐊᒻᒧᑦ downward
ammuumajjialangajuq ᐊᒻᒨᒪᔾᔨᐊᓚᖓᔪᖅ clam digging (he/she will be going...)
ammuumajuq ᐊᒻᒨᒪᔪᖅ clam
anaana ᐊᓈᓇ mother
anaanakkutinni ᐊᓈᓇᒃᑯᑎᓐᓂ at my mother's place
anaanatsiaq ᐊᓈᓇᑦᓯᐊᖅ grandmother
anarvik ᐊᓇᕐᕕᒃ washroom
anaullagat ᐊᓇᐅᓪᓚᒐᑦ drums
angak ᐊᖓᒃ uncle (mother's brother)
angijuk ᐊᖏᔪᒃ older sibling (same sex)
angijuq ᐊᖏᔪᖅ big (it is...)
angijuqtaq ᐊᖏᔪᖅᑕᖅ skirt
angijuqtaujaq ᐊᖏᔪᖅᑕᐅᔭᖅ amaut (skirted style)
angijuttiqpaaq ᐊᖏᔪᑦᑎᖅᐹᖅ eldest (the...)
angilaaq ᐊᖏᓛᖅ biggest (the...)
angiluaqtuq ᐊᖏᓗᐊᖅᑐᖅ too big (it is...)
anginiqsauva? ᐊᖏᓂᖅᓴᐅᕙ? bigger (is he/she/it...?)
angirraqsimaniaqtunga ᐊᖏᕐᕋᖅᓯᒪᓂᐊᖅᑐᖓ home (I will be at...)
angirraujuq ᐊᖏᕐᕋᐅᔪᖅ home (she goes...)
angunasuttuq ᐊᖑᓇᓱᑦᑐᖅ hunts (he/she...)
angut ᐊᖑᑦ man
anijuq ᐊᓂᔪᖅ leaves; goes out (he/she...)
anik ᐊᓂᒃ brother of a female
anikainnaqsimajuq ᐊᓂᑲᐃᓐᓇᖅᓯᒪᔪᖅ stepped out for a moment (he/she...)
anngakuluk ᐊᙵᑯᓗᒃ female's brother's child
annuraanut paniqsaut ᐊᓐᓄᕌᓄᑦ ᐸᓂᖅᓴᐅᑦ clothes dryer
annuraanut uasarvik ᐊᓐᓄᕌᓄᑦ ᐅᐊᓴᕐᕕᒃ washing machine
annuraaqtuq ᐊᓐᓄᕌᖅᑐᖅ gets dressed (he/she...)
annuraat ᐊᓐᓄᕌᑦ clothing
annuttuq ᐊᓐᓄᑦᑐᖅ frowns (he/she...)
anu ᐊᓄ harness (dog team)
anuraalliujjaujuq ᐊᓄᕌᓪᓕᐅᔾᔭᐅᔪᖅ caught in heavy winds
anuraaqtuq ᐊᓄᕌᖅᑐᖅ windy (it's...)
apijuq ᐊᐱᔪᖅ snow (falls and covers)
apiriguk ᐊᐱᕆᒍᒃ Ask him/her! (command)
apirijuq ᐊᐱᕆᔪᖅ asks (he/she...)
apisimajuq ᐊᐱᓯᒪᔪᖅ snow-covered
aput ᐊᐳᑦ snow
Apvituuk ᐊᑉᕕᑑᒃ Hopedale
aqiattuqtunga ᐊᕿᐊᑦᑐᖅᑐᖓ full (I am...)
aqiggiq ᐊᕿᒡᒋᖅ ptarmigan
aqittuq ᐊᕿᑦᑐᖅ soft
aqpik ᐊᖅᐱᒃ cloudberry
aqqut ᐊᖅᑯᑦ street
aqsaq ᐊᖅᓴᖅ ball
aqsarniit ᐊᖅᓴᕐᓃᑦ northern lights
aqturijuq ᐊᖅᑐᕆᔪᖅ unfit; he/she is in poor shape
aqua ᐊᖁᐊ bow of a boat
aquti ᐊᖁᑎ driver
aquttunnaqtuq ᐊᖁᑦᑐᓐᓇᖅᑐᖅ drive (he/she is able to...)
aquttuq ᐊᖁᑦᑐᖅ driving (he/she is...)
arnaq ᐊᕐᓇᖅ woman
arraagu ᐊᕐᕌᒍ year; next year
arraagulimaaq ᐊᕐᕌᒍᓕᒫᖅ all year
arraagumi tamatumani ᐊᕐᕌᒍᒥ ᑕᒪᑐᒪᓂ in this year
arraagutaaqqaummut ikaarvia ᐊᕐᕌᒍᑖᖅᑲᐅᒻᒧᑦ ᐃᑳᕐᕕᐊ New Year's
arraagutamaat ᐊᕐᕌᒍᑕᒫᑦ every year
arraani ᐊᕐᕌᓂ last year
Arvialiaqtuq ᐊᕐᕕᐊᓕᐊᖅᑐᖅ goes to Arviat (he/she...)
Arviat ᐊᕐᕕᐊᑦ Arviat
arvik ᐊᕐᕕᒃ bowhead whale
asingattauq ᐊᓯᖓᑦᑕᐅᖅ and another thing; another one
asivaqtuq ᐊᓯᕙᖅᑐᖅ hunting (he/she is going...)
asukuluk ᐊᓱᑯᓗᒃ oh yeah?; I see; that's good to hear.
ataaniittuq ᐊᑖᓃᑦᑐᖅ underneath (it is)
ataanuarlutit ᐊᑖᓄᐊᕐᓗᑎᑦ Go downstairs! (command)
ataata ᐊᑖᑕ father
ataatatsiaq ᐊᑖᑕᑦᓯᐊᖅ grandfather
atajuq ᐊᑕᔪᖅ dress
atausiq ᐊᑕᐅᓯᖅ one
atausirmik ᐊᑕᐅᓯᕐᒥᒃ one of something
atigi ᐊᑎᒋ caribou parka with fur inside
atii ᐊᑏ Come on!; Let's go!; Go ahead.
atiliuqtara ᐊᑎᓕᐅᖅᑕᕋ sign it (I...)
attak ᐊᑦᑕᒃ aunt (father's sister)
attunaaq ᐊᑦᑐᓈᖅ rope
atuqtaujuq ᐊᑐᖅᑕᐅᔪᖅ It is used (passive)
atuqtuq ᐊᑐᖅᑐᖅ uses something (he/she...)
aujalimaaq ᐊᐅᔭᓕᒫᖅ all summer
aujaq ᐊᐅᔭᖅ summer
aujatamaat ᐊᐅᔭᑕᒫᑦ every summer
aujuittuq ᐊᐅᔪᐃᑦᑐᖅ glacier
auk ᐊᐅᒃ blood
aulajuq ᐊᐅᓚᔪᖅ motion (it's in...)
aulausirijikkut ᐊᐅᓚᐅᓯᕆᔨᒃᑯᑦ power plant
aulautit ᐊᐅᓚᐅᑎᑦ outboard motor
aullalaaqtuq ᐊᐅᓪᓚᓛᖅᑐᖅ depart, he/she will.... (tomorrow or later)
aullalauqtuq ᐊᐅᓪᓚᓚᐅᖅᑐᖅ departed, he/she... (yesterday or earlier)
aullaliqtuq ᐊᐅᓪᓚᓕᖅᑐᖅ departing (he/she is...right now)
aullaqqaujuq ᐊᐅᓪᓚᖅᑲᐅᔪᖅ departed, he/she...(earlier today)
aullaqsimajuq ᐊᐅᓪᓚᖅᓯᒪᔪᖅ away; out of town (he is...)
aullaqtuq ᐊᐅᓪᓚᖅᑐᖅ departs (he/she...)
aullarniaqtuq ᐊᐅᓪᓚᕐᓂᐊᖅᑐᖅ depart, he/she will... (later today)
aunaaqtunga ᐊᐅᓈᖅᑐᖓ bleeding (I am...)
aupajaangajuq ᐊᐅᐸᔮᖓᔪᖅ orange
aupajaattuq ᐊᐅᐸᔮᑦᑐᖅ pink
aupalakaattunga ᐊᐅᐸᓚᑳᑦᑐᖓ chicken pox (I have...)
aupaqtuq ᐊᐅᐸᖅᑐᖅ red
Ausuittuq ᐊᐅᓱᐃᑦᑐᖅ Grise Fiord
avalu ᐊᕙᓗ wall
avani ᐊᕙᓂ over there (in that area...)
Avatilirijikkut ᐊᕙᑎᓕᕆᔨᒃᑯᑦ Department of the Environment
avatit ᐊᕙᑎᑦ twenty
avinngannguaq (qarasaujaq) ᐊᕕᙵᙳᐊᖅ (ᖃᕋᓴᐅᔭᖅ) mouse (computer)
avinngaq ᐊᕕᙵᖅ lemming
"""
    
    logger.info("Extracting vocabulary from Tusaalanga glossary...")
    
    translations = {}
    lines = glossary_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # Parse pattern: inuktitut_word syllabics english_definition
        parts = line.split(' ', 2)
        if len(parts) >= 3:
            inuktitut_roman = parts[0]
            syllabics = parts[1]
            english_def = parts[2]
            
            # Extract key English words from definition
            english_words = extract_english_keywords(english_def)
            
            for eng_word in english_words:
                if len(eng_word) > 2:  # Skip very short words
                    translations[eng_word] = {
                        'translation': syllabics,
                        'confidence': 50,  # Medium confidence for web sources
                        'source': 'tusaalanga',
                        'roman': inuktitut_roman
                    }
    
    logger.info(f"Extracted {len(translations)} translations from Tusaalanga")
    return translations


def extract_english_keywords(definition):
    """Extract key English words from a definition."""
    
    # Remove parenthetical content and common words
    definition = re.sub(r'\([^)]*\)', '', definition)
    definition = definition.lower()
    
    # Remove common words that aren't useful for translation
    stop_words = {
        'he', 'she', 'it', 'is', 'are', 'was', 'were', 'the', 'a', 'an', 'and', 'or', 'but',
        'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into',
        'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among',
        'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'do', 'does',
        'did', 'has', 'have', 'had', 'be', 'been', 'being', 'this', 'that', 'these', 'those',
        'i', 'you', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their',
        'goes', 'go', 'went', 'come', 'came', 'get', 'got', 'make', 'made', 'take', 'took',
        'something', 'someone', 'somewhere', 'somehow', 'somewhat'
    }
    
    # Extract words
    words = re.findall(r'\b[a-z]+\b', definition)
    keywords = []
    
    for word in words:
        if word not in stop_words and len(word) > 2:
            keywords.append(word)
    
    # Also extract key phrases
    if 'thank you' in definition:
        keywords.append('thank you')
    if 'good morning' in definition:
        keywords.append('good morning')
    if 'good evening' in definition:
        keywords.append('good evening')
    
    return keywords


def search_additional_sources():
    """Search for additional online dictionary sources."""
    
    logger.info("Searching for additional Inuktitut dictionary sources...")
    
    # Manual collection of known good translations from various sources
    additional_translations = {
        # Basic greetings and common phrases
        'hello': {'translation': 'ᐊᐃ', 'confidence': 80, 'source': 'manual'},
        'goodbye': {'translation': 'ᑕᕝᕙᓂ', 'confidence': 70, 'source': 'manual'},
        'please': {'translation': 'ᖁᔭᓐᓇᒦᒃ', 'confidence': 60, 'source': 'manual'},
        'sorry': {'translation': 'ᒪᒥᐊᓇᖅ', 'confidence': 70, 'source': 'manual'},
        'excuse me': {'translation': 'ᒪᒥᐊᓇᖅ', 'confidence': 60, 'source': 'manual'},
        
        # Family terms
        'mother': {'translation': 'ᐊᓈᓇ', 'confidence': 90, 'source': 'manual'},
        'father': {'translation': 'ᐊᑖᑕ', 'confidence': 90, 'source': 'manual'},
        'grandmother': {'translation': 'ᐊᓈᓇᑦᓯᐊᖅ', 'confidence': 80, 'source': 'manual'},
        'grandfather': {'translation': 'ᐊᑖᑕᑦᓯᐊᖅ', 'confidence': 80, 'source': 'manual'},
        'child': {'translation': 'ᓱᕈᓯ', 'confidence': 80, 'source': 'manual'},
        'baby': {'translation': 'ᓄᑕᕋᖅ', 'confidence': 80, 'source': 'manual'},
        
        # Numbers
        'one': {'translation': 'ᐊᑕᐅᓯᖅ', 'confidence': 90, 'source': 'manual'},
        'two': {'translation': 'ᒪᕐᕉᒃ', 'confidence': 90, 'source': 'manual'},
        'three': {'translation': 'ᐱᖓᓱᑦ', 'confidence': 90, 'source': 'manual'},
        'four': {'translation': 'ᓯᑕᒪᑦ', 'confidence': 90, 'source': 'manual'},
        'five': {'translation': 'ᑕᓪᓕᒪᑦ', 'confidence': 90, 'source': 'manual'},
        
        # Colors
        'white': {'translation': 'ᖃᐅᔨᒪᔪᖅ', 'confidence': 70, 'source': 'manual'},
        'black': {'translation': 'ᖁᕐᓂᖅ', 'confidence': 70, 'source': 'manual'},
        'red': {'translation': 'ᐊᐅᐸᖅᑐᖅ', 'confidence': 80, 'source': 'manual'},
        'blue': {'translation': 'ᑐᓂᔭᖅ', 'confidence': 70, 'source': 'manual'},
        'green': {'translation': 'ᐅᕐᓱᖅᑐᖅ', 'confidence': 70, 'source': 'manual'},
        
        # Animals
        'dog': {'translation': 'ᕿᒻᒥᖅ', 'confidence': 90, 'source': 'manual'},
        'cat': {'translation': 'ᐸᐅᓯ', 'confidence': 80, 'source': 'manual'},
        'bear': {'translation': 'ᓇᓄᖅ', 'confidence': 90, 'source': 'manual'},
        'seal': {'translation': 'ᓇᑦᓯᖅ', 'confidence': 90, 'source': 'manual'},
        'whale': {'translation': 'ᐊᕐᕕᒃ', 'confidence': 90, 'source': 'manual'},
        'bird': {'translation': 'ᑎᖕᒥᐊᖅ', 'confidence': 80, 'source': 'manual'},
        'fish': {'translation': 'ᐃᖃᓗᒃ', 'confidence': 90, 'source': 'manual'},
        
        # Food
        'food': {'translation': 'ᓂᕿ', 'confidence': 90, 'source': 'manual'},
        'water': {'translation': 'ᐃᒥᖅ', 'confidence': 90, 'source': 'manual'},
        'tea': {'translation': 'ᓴᐃ', 'confidence': 80, 'source': 'manual'},
        'coffee': {'translation': 'ᑳᐱ', 'confidence': 80, 'source': 'manual'},
        'bread': {'translation': 'ᐊᓕᐊᓇᐃᑦ', 'confidence': 70, 'source': 'manual'},
        'meat': {'translation': 'ᓂᖅᑎ', 'confidence': 80, 'source': 'manual'},
        
        # Weather
        'cold': {'translation': 'ᐅᓇᖅ', 'confidence': 80, 'source': 'manual'},
        'hot': {'translation': 'ᐅᓄᖅ', 'confidence': 70, 'source': 'manual'},
        'wind': {'translation': 'ᐊᓄᕆ', 'confidence': 80, 'source': 'manual'},
        'rain': {'translation': 'ᓯᓚᓗᒃ', 'confidence': 70, 'source': 'manual'},
        'snow': {'translation': 'ᐊᐳᑦ', 'confidence': 90, 'source': 'manual'},
        'ice': {'translation': 'ᓯᑯ', 'confidence': 90, 'source': 'manual'},
        
        # Time
        'today': {'translation': 'ᐅᓪᓗᒥ', 'confidence': 80, 'source': 'manual'},
        'tomorrow': {'translation': 'ᖃᐅᔨᒪᔪᖅ', 'confidence': 70, 'source': 'manual'},
        'yesterday': {'translation': 'ᐃᑉᐱᒋᔭᐅᔪᖅ', 'confidence': 70, 'source': 'manual'},
        'morning': {'translation': 'ᐅᓪᓗᒃ', 'confidence': 80, 'source': 'manual'},
        'evening': {'translation': 'ᐅᓐᓄᒃ', 'confidence': 80, 'source': 'manual'},
        'night': {'translation': 'ᐅᓐᓄᒃ', 'confidence': 80, 'source': 'manual'},
        
        # Body parts
        'head': {'translation': 'ᓂᐊᖅ', 'confidence': 80, 'source': 'manual'},
        'eye': {'translation': 'ᐃᔨ', 'confidence': 80, 'source': 'manual'},
        'nose': {'translation': 'ᕿᖓᖅ', 'confidence': 80, 'source': 'manual'},
        'mouth': {'translation': 'ᖃᓂᖅ', 'confidence': 80, 'source': 'manual'},
        'hand': {'translation': 'ᐊᒡᒐᑦ', 'confidence': 90, 'source': 'manual'},
        'foot': {'translation': 'ᐃᓯᖅ', 'confidence': 80, 'source': 'manual'},
    }
    
    logger.info(f"Added {len(additional_translations)} manual translations")
    return additional_translations


def merge_dictionaries():
    """Merge web-scraped dictionary with existing corpus dictionary."""
    
    logger.info("Merging web dictionary with corpus dictionary...")
    
    # Load existing dictionary
    existing_dict_path = Path("nunavut_mt/models/dictionary/word_dictionary.json")
    if existing_dict_path.exists():
        with open(existing_dict_path, 'r', encoding='utf-8') as f:
            existing_dict = json.load(f)
    else:
        existing_dict = {}
    
    # Get web translations
    web_translations = extract_tusaalanga_glossary()
    additional_translations = search_additional_sources()
    
    # Merge all dictionaries
    merged_dict = existing_dict.copy()
    
    # Add web translations (lower priority than corpus)
    for word, data in web_translations.items():
        if word not in merged_dict:
            merged_dict[word] = data
        elif merged_dict[word]['confidence'] < data['confidence']:
            # Only replace if web source has higher confidence
            merged_dict[word] = data
    
    # Add manual translations (high priority)
    for word, data in additional_translations.items():
        if word not in merged_dict:
            merged_dict[word] = data
        elif data['confidence'] >= merged_dict[word]['confidence']:
            merged_dict[word] = data
    
    # Save merged dictionary
    output_dir = Path("nunavut_mt/models/web_dictionary")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_dir / "merged_dictionary.json", 'w', encoding='utf-8') as f:
        json.dump(merged_dict, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Merged dictionary saved with {len(merged_dict)} total entries")
    
    # Show statistics
    corpus_count = sum(1 for v in merged_dict.values() if v.get('source') not in ['tusaalanga', 'manual'])
    web_count = sum(1 for v in merged_dict.values() if v.get('source') == 'tusaalanga')
    manual_count = sum(1 for v in merged_dict.values() if v.get('source') == 'manual')
    
    print(f"\n📊 Merged Dictionary Statistics:")
    print(f"   - Corpus translations: {corpus_count}")
    print(f"   - Web scraped: {web_count}")
    print(f"   - Manual additions: {manual_count}")
    print(f"   - Total: {len(merged_dict)}")
    
    # Show some examples
    print(f"\n📚 Sample New Translations:")
    new_entries = [(k, v) for k, v in merged_dict.items() 
                   if v.get('source') in ['tusaalanga', 'manual']]
    new_entries.sort(key=lambda x: x[1]['confidence'], reverse=True)
    
    for word, data in new_entries[:15]:
        conf = data['confidence']
        trans = data['translation']
        source = data.get('source', 'unknown')
        print(f"   {word:15} → {trans:15} ({source}, conf: {conf})")
    
    return merged_dict


if __name__ == "__main__":
    print("🌐 Building Web-Enhanced Dictionary")
    print("=" * 50)
    
    try:
        merged_dict = merge_dictionaries()
        
        print(f"\n✅ Web dictionary enhancement completed!")
        print(f"Next: Test with 'python nunavut_mt/enhanced_translator.py'")
        
    except Exception as e:
        logger.error(f"Failed to build web dictionary: {e}")
        import traceback
        traceback.print_exc()
