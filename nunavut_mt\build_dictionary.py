#!/usr/bin/env python3
"""
Build a practical dictionary-based translator from the parallel corpus.
This will give much better results than the undertrained neural model.
"""

import re
from collections import defaultdict, Counter
from pathlib import Path
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_word_pairs():
    """Extract word-to-word translation pairs from the parallel corpus."""
    
    data_dir = Path("nunavut_mt/data/processed")
    en_file = data_dir / "train.en"
    iu_file = data_dir / "train.iu"
    
    if not en_file.exists() or not iu_file.exists():
        logger.error("Training files not found!")
        return {}
    
    logger.info("Loading parallel sentences...")
    
    with open(en_file, 'r', encoding='utf-8') as f:
        en_lines = [line.strip() for line in f if line.strip()]
    
    with open(iu_file, 'r', encoding='utf-8') as f:
        iu_lines = [line.strip() for line in f if line.strip()]
    
    logger.info(f"Loaded {len(en_lines)} English and {len(iu_lines)} Inuktitut sentences")
    
    # Extract translation pairs
    translation_pairs = defaultdict(Counter)
    phrase_pairs = defaultdict(Counter)
    
    # Take a sample for processing
    max_pairs = 50000
    pairs_to_process = min(len(en_lines), len(iu_lines), max_pairs)
    
    logger.info(f"Processing {pairs_to_process} sentence pairs...")
    
    for i in range(pairs_to_process):
        en_sent = en_lines[i].lower()
        iu_sent = iu_lines[i]
        
        # Clean sentences
        en_sent = re.sub(r'[^\w\s]', '', en_sent)
        iu_sent = re.sub(r'[^\w\s\u1400-\u167F]', '', iu_sent)  # Keep Inuktitut syllabics
        
        en_words = en_sent.split()
        iu_words = iu_sent.split()
        
        # Skip very long or very short sentences
        if len(en_words) < 2 or len(en_words) > 20 or len(iu_words) < 1 or len(iu_words) > 20:
            continue
        
        # For short sentences, try to align the whole sentence
        if len(en_words) <= 5 and len(iu_words) <= 5:
            phrase_pairs[en_sent.strip()][iu_sent.strip()] += 1
        
        # Look for common patterns
        if "thank you" in en_sent and "ᖁᔭᓐᓇᒦᒃ" in iu_sent:
            translation_pairs["thank you"]["ᖁᔭᓐᓇᒦᒃ"] += 1
        
        if "mr speaker" in en_sent or "mr. speaker" in en_sent:
            for iu_word in iu_words:
                if len(iu_word) > 3:  # Skip short words
                    translation_pairs["mr speaker"][iu_word] += 1
        
        if "government" in en_sent:
            for iu_word in iu_words:
                if len(iu_word) > 3:
                    translation_pairs["government"][iu_word] += 1
        
        # Simple word co-occurrence
        for en_word in en_words:
            if len(en_word) > 3:  # Skip short words
                for iu_word in iu_words:
                    if len(iu_word) > 2:
                        translation_pairs[en_word][iu_word] += 1
    
    logger.info(f"Extracted {len(translation_pairs)} word translation candidates")
    logger.info(f"Extracted {len(phrase_pairs)} phrase translation candidates")
    
    return translation_pairs, phrase_pairs


def build_dictionary():
    """Build a practical translation dictionary."""
    
    logger.info("Building translation dictionary...")
    
    translation_pairs, phrase_pairs = extract_word_pairs()
    
    # Build final dictionary with confidence scores
    dictionary = {}
    phrase_dictionary = {}
    
    # Process word pairs
    for en_word, iu_candidates in translation_pairs.items():
        if len(iu_candidates) == 0:
            continue
        
        # Get the most frequent translation
        best_iu, count = iu_candidates.most_common(1)[0]
        
        # Only include if we have reasonable confidence
        if count >= 3:  # Appeared at least 3 times
            dictionary[en_word] = {
                'translation': best_iu,
                'confidence': count,
                'alternatives': dict(iu_candidates.most_common(3))
            }
    
    # Process phrase pairs
    for en_phrase, iu_candidates in phrase_pairs.items():
        if len(iu_candidates) == 0:
            continue
        
        best_iu, count = iu_candidates.most_common(1)[0]
        
        if count >= 2:  # Appeared at least 2 times
            phrase_dictionary[en_phrase] = {
                'translation': best_iu,
                'confidence': count
            }
    
    logger.info(f"Built dictionary with {len(dictionary)} words and {len(phrase_dictionary)} phrases")
    
    # Add some manual high-confidence translations
    manual_translations = {
        "thank you": {"translation": "ᖁᔭᓐᓇᒦᒃ", "confidence": 100},
        "hello": {"translation": "ᐊᐃ", "confidence": 100},
        "yes": {"translation": "ᐄ", "confidence": 100},
        "no": {"translation": "ᐊᒃᑲ", "confidence": 100},
        "mr speaker": {"translation": "ᐅᖃᖅᑎ", "confidence": 100},
        "mr. speaker": {"translation": "ᐅᖃᖅᑎ", "confidence": 100},
        "speaker": {"translation": "ᐅᖃᖅᑎ", "confidence": 100},
        "government": {"translation": "ᒐᕙᒪᒃᑯᑦ", "confidence": 100},
        "good morning": {"translation": "ᐅᓪᓗᒃ ᐱᐅᓂᖅ", "confidence": 100},
        "good evening": {"translation": "ᐅᓐᓄᒃ ᐱᐅᓂᖅ", "confidence": 100}
    }
    
    # Merge manual translations
    for en, iu_data in manual_translations.items():
        dictionary[en] = iu_data
    
    return dictionary, phrase_dictionary


def save_dictionary(dictionary, phrase_dictionary):
    """Save the dictionary to files."""
    
    dict_dir = Path("nunavut_mt/models/dictionary")
    dict_dir.mkdir(parents=True, exist_ok=True)
    
    # Save word dictionary
    with open(dict_dir / "word_dictionary.json", 'w', encoding='utf-8') as f:
        json.dump(dictionary, f, ensure_ascii=False, indent=2)
    
    # Save phrase dictionary
    with open(dict_dir / "phrase_dictionary.json", 'w', encoding='utf-8') as f:
        json.dump(phrase_dictionary, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Saved dictionaries to {dict_dir}")
    
    # Print some examples
    print("\n📚 Sample Dictionary Entries:")
    print("=" * 40)
    
    # Show high-confidence entries
    high_conf_entries = [(k, v) for k, v in dictionary.items() 
                        if v.get('confidence', 0) >= 10]
    high_conf_entries.sort(key=lambda x: x[1]['confidence'], reverse=True)
    
    for en, iu_data in high_conf_entries[:15]:
        conf = iu_data['confidence']
        trans = iu_data['translation']
        print(f"  {en:15} → {trans:15} (confidence: {conf})")
    
    print(f"\n📊 Dictionary Stats:")
    print(f"  - Word translations: {len(dictionary)}")
    print(f"  - Phrase translations: {len(phrase_dictionary)}")
    print(f"  - High confidence (≥10): {len(high_conf_entries)}")


if __name__ == "__main__":
    print("🔨 Building Practical Translation Dictionary")
    print("=" * 50)
    
    try:
        dictionary, phrase_dictionary = build_dictionary()
        save_dictionary(dictionary, phrase_dictionary)
        
        print("\n✅ Dictionary built successfully!")
        print("Next: Run 'python nunavut_mt/dictionary_translator.py' to test it")
        
    except Exception as e:
        logger.error(f"Failed to build dictionary: {e}")
        import traceback
        traceback.print_exc()
