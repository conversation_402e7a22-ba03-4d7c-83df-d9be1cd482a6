#!/usr/bin/env python3
"""
Dictionary-based translator that actually works.
This uses the extracted dictionary from the parallel corpus.
"""

import json
import re
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DictionaryTranslator:
    """Dictionary-based English-Inuktitut translator."""
    
    def __init__(self):
        """Load the dictionaries."""
        dict_dir = Path("nunavut_mt/models/dictionary")
        
        # Load word dictionary
        word_dict_path = dict_dir / "word_dictionary.json"
        if word_dict_path.exists():
            with open(word_dict_path, 'r', encoding='utf-8') as f:
                self.word_dict = json.load(f)
        else:
            logger.error("Word dictionary not found!")
            self.word_dict = {}
        
        # Load phrase dictionary
        phrase_dict_path = dict_dir / "phrase_dictionary.json"
        if phrase_dict_path.exists():
            with open(phrase_dict_path, 'r', encoding='utf-8') as f:
                self.phrase_dict = json.load(f)
        else:
            logger.warning("Phrase dictionary not found!")
            self.phrase_dict = {}
        
        logger.info(f"Loaded {len(self.word_dict)} words and {len(self.phrase_dict)} phrases")
    
    def translate_en2iu(self, text):
        """Translate English to Inuktitut."""
        
        # Clean input
        original_text = text
        text = text.lower().strip()
        
        # Try phrase translation first
        if text in self.phrase_dict:
            return self.phrase_dict[text]['translation']
        
        # Try word-by-word translation
        words = re.findall(r'\b\w+\b', text)
        translated_words = []
        
        for word in words:
            if word in self.word_dict:
                translation = self.word_dict[word]['translation']
                confidence = self.word_dict[word]['confidence']
                
                # Only use high-confidence translations
                if confidence >= 5:
                    translated_words.append(translation)
                else:
                    translated_words.append(f"[{word}]")  # Mark uncertain
            else:
                # Check for partial matches
                found_match = False
                for dict_word in self.word_dict:
                    if word in dict_word or dict_word in word:
                        if self.word_dict[dict_word]['confidence'] >= 10:
                            translated_words.append(self.word_dict[dict_word]['translation'])
                            found_match = True
                            break
                
                if not found_match:
                    translated_words.append(f"[{word}]")
        
        if translated_words:
            return " ".join(translated_words)
        else:
            return f"[No translation found for: {original_text}]"
    
    def translate_iu2en(self, text):
        """Translate Inuktitut to English (reverse lookup)."""
        
        # Create reverse dictionary
        reverse_dict = {}
        for en_word, iu_data in self.word_dict.items():
            iu_word = iu_data['translation']
            confidence = iu_data['confidence']
            
            if confidence >= 5:
                if iu_word not in reverse_dict or confidence > reverse_dict[iu_word]['confidence']:
                    reverse_dict[iu_word] = {'translation': en_word, 'confidence': confidence}
        
        # Try direct lookup
        text = text.strip()
        if text in reverse_dict:
            return reverse_dict[text]['translation']
        
        # Try word-by-word
        words = text.split()
        translated_words = []
        
        for word in words:
            if word in reverse_dict:
                translated_words.append(reverse_dict[word]['translation'])
            else:
                translated_words.append(f"[{word}]")
        
        if translated_words:
            return " ".join(translated_words)
        else:
            return f"[No translation found for: {text}]"


def test_dictionary_translator():
    """Test the dictionary translator."""
    
    print("🔍 Testing Dictionary-Based Translator")
    print("=" * 50)
    
    translator = DictionaryTranslator()
    
    # Test sentences
    test_sentences = [
        "Thank you",
        "Thank you, Mr. Speaker",
        "Hello",
        "Good morning",
        "The government",
        "Minister",
        "Nunavut",
        "Questions",
        "Members",
        "Interpretation"
    ]
    
    print("\n🚀 Dictionary Translation Results:")
    print("=" * 60)
    
    success_count = 0
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n{i:2d}. EN: {sentence}")
        
        # Translate EN → IU
        result = translator.translate_en2iu(sentence)
        print(f"    IU: {result}")
        
        # Check if translation looks good (no brackets)
        if "[" not in result and "]" not in result and len(result) > 1:
            success_count += 1
            print(f"    ✅ Good translation")
            
            # Try reverse translation
            reverse = translator.translate_iu2en(result)
            print(f"    ←: {reverse}")
        else:
            print(f"    ⚠️  Partial/uncertain translation")
    
    print(f"\n📊 Results Summary:")
    print(f"   - Good translations: {success_count}/{len(test_sentences)}")
    print(f"   - Success rate: {success_count/len(test_sentences)*100:.1f}%")
    
    return translator


def interactive_dictionary_mode():
    """Interactive dictionary translation."""
    
    print("\n🚀 Interactive Dictionary Translation")
    print("Commands: 'en2iu <text>' or 'iu2en <text>' or 'quit'")
    print("Example: en2iu Thank you, Mr. Speaker")
    print("-" * 50)
    
    translator = DictionaryTranslator()
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if user_input.startswith('en2iu '):
                text = user_input[6:]
                result = translator.translate_en2iu(text)
                print(f"EN → IU: {result}")
                
            elif user_input.startswith('iu2en '):
                text = user_input[6:]
                result = translator.translate_iu2en(text)
                print(f"IU → EN: {result}")
                
            else:
                print("Please use format: 'en2iu <text>' or 'iu2en <text>'")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except EOFError:
            print("\nInput stream ended. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


def show_dictionary_stats():
    """Show dictionary statistics."""
    
    print("\n📊 Dictionary Statistics")
    print("-" * 30)
    
    translator = DictionaryTranslator()
    
    # Analyze confidence levels
    confidence_levels = [0, 5, 10, 25, 50, 100]
    
    for min_conf in confidence_levels:
        high_conf = sum(1 for word, data in translator.word_dict.items() 
                       if data.get('confidence', 0) >= min_conf)
        print(f"  Confidence ≥{min_conf:3d}: {high_conf:4d} words")
    
    # Show top translations
    print(f"\n🏆 Top 10 Most Confident Translations:")
    sorted_words = sorted(translator.word_dict.items(), 
                         key=lambda x: x[1].get('confidence', 0), 
                         reverse=True)
    
    for i, (en_word, iu_data) in enumerate(sorted_words[:10], 1):
        conf = iu_data['confidence']
        trans = iu_data['translation']
        print(f"  {i:2d}. {en_word:15} → {trans:15} ({conf:4d})")


if __name__ == "__main__":
    print("📚 Dictionary-Based Nunavut MT")
    print("=" * 40)
    
    # Show stats
    show_dictionary_stats()
    
    # Test the translator
    translator = test_dictionary_translator()
    
    # Offer interactive mode
    print(f"\n💡 Want to try interactive mode? (y/n)")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            interactive_dictionary_mode()
    except (KeyboardInterrupt, EOFError):
        print("\nGoodbye!")
