#!/usr/bin/env python3
"""
Neural model training script for Nunavut MT.

This script trains an actual neural translation model using the processed corpus.
"""

import os
import sys
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import sentencepiece as spm
from pathlib import Path
import json
import time
from tqdm import tqdm
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SimpleTransformer(nn.Module):
    """Simple transformer model for translation."""
    
    def __init__(self, vocab_size, d_model=256, nhead=8, num_layers=6, max_seq_len=512):
        super().__init__()
        self.d_model = d_model
        self.vocab_size = vocab_size
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.pos_encoding = nn.Parameter(torch.randn(max_seq_len, d_model))
        
        # Transformer
        encoder_layer = nn.TransformerEncoderLayer(d_model, nhead, batch_first=True)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        
        # Output projection
        self.output_proj = nn.Linear(d_model, vocab_size)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, src, src_mask=None):
        # Add positional encoding
        seq_len = src.size(1)
        pos_enc = self.pos_encoding[:seq_len].unsqueeze(0).expand(src.size(0), -1, -1)
        
        # Embed and add positional encoding
        x = self.embedding(src) * (self.d_model ** 0.5)
        x = x + pos_enc
        x = self.dropout(x)
        
        # Transform
        x = self.transformer(x, src_key_padding_mask=src_mask)
        
        # Project to vocabulary
        return self.output_proj(x)


class TranslationDataset(Dataset):
    """Dataset for translation training."""
    
    def __init__(self, en_file, iu_file, tokenizer, max_len=256):
        self.tokenizer = tokenizer
        self.max_len = max_len
        
        # Load data
        with open(en_file, 'r', encoding='utf-8') as f:
            en_lines = [line.strip() for line in f if line.strip()]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_lines = [line.strip() for line in f if line.strip()]
        
        # Take a subset for faster training
        max_samples = 50000  # Limit for demonstration
        self.en_lines = en_lines[:max_samples]
        self.iu_lines = iu_lines[:max_samples]
        
        logger.info(f"Loaded {len(self.en_lines)} training pairs")
    
    def __len__(self):
        return len(self.en_lines)
    
    def __getitem__(self, idx):
        en_text = self.en_lines[idx]
        iu_text = self.iu_lines[idx]
        
        # Tokenize with direction tokens
        en_tokens = self.tokenizer.encode_as_ids(f"<en2iu> {en_text}")
        iu_tokens = self.tokenizer.encode_as_ids(f"{iu_text}")
        
        # Truncate if too long
        if len(en_tokens) > self.max_len:
            en_tokens = en_tokens[:self.max_len]
        if len(iu_tokens) > self.max_len:
            iu_tokens = iu_tokens[:self.max_len]
        
        return {
            'input_ids': torch.tensor(en_tokens, dtype=torch.long),
            'target_ids': torch.tensor(iu_tokens, dtype=torch.long)
        }


def collate_fn(batch):
    """Collate function for batching."""
    input_ids = [item['input_ids'] for item in batch]
    target_ids = [item['target_ids'] for item in batch]
    
    # Pad sequences
    max_input_len = max(len(seq) for seq in input_ids)
    max_target_len = max(len(seq) for seq in target_ids)
    
    padded_inputs = []
    padded_targets = []
    input_masks = []
    
    for inp, tgt in zip(input_ids, target_ids):
        # Pad input
        inp_padded = torch.cat([inp, torch.zeros(max_input_len - len(inp), dtype=torch.long)])
        padded_inputs.append(inp_padded)
        
        # Create mask (True for padding positions)
        mask = torch.cat([torch.zeros(len(inp), dtype=torch.bool), 
                         torch.ones(max_input_len - len(inp), dtype=torch.bool)])
        input_masks.append(mask)
        
        # Pad target
        tgt_padded = torch.cat([tgt, torch.zeros(max_target_len - len(tgt), dtype=torch.long)])
        padded_targets.append(tgt_padded)
    
    return {
        'input_ids': torch.stack(padded_inputs),
        'target_ids': torch.stack(padded_targets),
        'input_mask': torch.stack(input_masks)
    }


def train_model():
    """Train the neural translation model."""
    
    # Paths
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    data_dir = Path("nunavut_mt/data/processed")
    model_dir = Path("nunavut_mt/models/neural")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    vocab_size = tokenizer.get_piece_size()
    
    # Create dataset
    logger.info("Creating dataset...")
    dataset = TranslationDataset(
        en_file=data_dir / "train.en",
        iu_file=data_dir / "train.iu",
        tokenizer=tokenizer
    )
    
    # Create dataloader
    dataloader = DataLoader(
        dataset, 
        batch_size=16,  # Small batch for CPU
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=0  # No multiprocessing for Windows compatibility
    )
    
    # Create model
    logger.info("Creating model...")
    model = SimpleTransformer(vocab_size, d_model=256, nhead=8, num_layers=4)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss(ignore_index=0)  # Ignore padding
    optimizer = torch.optim.Adam(model.parameters(), lr=0.0001)
    
    # Training loop
    logger.info("Starting training...")
    model.train()
    
    num_epochs = 2  # Small number for demonstration
    total_loss = 0
    step = 0
    
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch + 1}/{num_epochs}")
        
        epoch_loss = 0
        progress_bar = tqdm(dataloader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            input_ids = batch['input_ids']
            target_ids = batch['target_ids']
            input_mask = batch['input_mask']
            
            # Forward pass
            outputs = model(input_ids, src_mask=input_mask)
            
            # Calculate loss (shift targets for language modeling)
            loss = criterion(outputs[:, :-1].contiguous().view(-1, vocab_size), 
                           target_ids[:, 1:].contiguous().view(-1))
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            epoch_loss += loss.item()
            step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{epoch_loss / (progress_bar.n + 1):.4f}"
            })
            
            # Log periodically
            if step % 100 == 0:
                logger.info(f"Step {step}: loss = {loss.item():.4f}")
        
        logger.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(dataloader):.4f}")
    
    # Save model
    logger.info("Saving model...")
    model_path = model_dir / "translation_model.pt"
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'model_config': {
            'd_model': 256,
            'nhead': 8,
            'num_layers': 4,
            'max_seq_len': 512
        }
    }, model_path)
    
    # Save training info
    training_info = {
        'vocab_size': vocab_size,
        'training_samples': len(dataset),
        'epochs': num_epochs,
        'final_loss': total_loss / step if step > 0 else 0,
        'model_path': str(model_path)
    }
    
    with open(model_dir / "training_info.json", 'w') as f:
        json.dump(training_info, f, indent=2)
    
    logger.info(f"Training completed! Model saved to {model_path}")
    logger.info(f"Training info saved to {model_dir / 'training_info.json'}")
    
    return model_path


def test_trained_model(model_path):
    """Test the trained model with sample translations."""
    
    # Load tokenizer
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    vocab_size = tokenizer.get_piece_size()
    
    # Load model
    logger.info("Loading trained model...")
    checkpoint = torch.load(model_path, map_location='cpu')
    model = SimpleTransformer(vocab_size, d_model=256, nhead=8, num_layers=4)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # Test sentences
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "Hello, how are you?",
        "The government is working hard.",
        "Good morning, everyone."
    ]
    
    logger.info("Testing trained model:")
    print("\n" + "="*60)
    print("NEURAL MODEL TRANSLATIONS")
    print("="*60)
    
    with torch.no_grad():
        for sentence in test_sentences:
            # Tokenize input
            input_tokens = tokenizer.encode_as_ids(f"<en2iu> {sentence}")
            input_tensor = torch.tensor([input_tokens], dtype=torch.long)
            
            # Generate (simple greedy decoding)
            output = model(input_tensor)
            predicted_ids = torch.argmax(output, dim=-1)[0]
            
            # Decode output
            predicted_text = tokenizer.decode_ids(predicted_ids.tolist())
            
            print(f"EN: {sentence}")
            print(f"IU: {predicted_text}")
            print("-" * 40)


if __name__ == "__main__":
    logger.info("Starting neural model training for Nunavut MT")
    
    try:
        # Train the model
        model_path = train_model()
        
        # Test the trained model
        test_trained_model(model_path)
        
        logger.info("Neural model training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise
