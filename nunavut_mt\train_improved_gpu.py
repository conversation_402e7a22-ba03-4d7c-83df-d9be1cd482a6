#!/usr/bin/env python3
"""
Improved GPU training with more data and better architecture.
"""

import os
import sys
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import sentencepiece as spm
from pathlib import Path
import json
import time
from tqdm import tqdm
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def train_improved_model():
    """Train an improved model with more data."""
    
    # Check GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load the existing trained model
    model_path = "nunavut_mt/models/efficient_gpu/efficient_translation_model.pt"
    
    if not Path(model_path).exists():
        logger.error("No trained model found. Please run train_efficient_gpu.py first.")
        return
    
    # Load tokenizer
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    vocab_size = checkpoint['vocab_size']
    
    # Import the model class
    from train_efficient_gpu import EfficientTranslator, SimpleDataset, collate_fn
    
    # Create model and load weights
    model = EfficientTranslator(
        vocab_size=vocab_size, 
        d_model=256,
        nhead=4, 
        num_layers=3,
        max_seq_len=64
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    
    logger.info("Loaded existing trained model for continued training")
    
    # Create larger dataset
    data_dir = Path("nunavut_mt/data/processed")
    dataset = SimpleDataset(
        en_file=data_dir / "train.en",
        iu_file=data_dir / "train.iu",
        tokenizer=tokenizer,
        max_len=64,
        max_samples=20000  # More data
    )
    
    # Create dataloader
    dataloader = DataLoader(
        dataset, 
        batch_size=32,  # Larger batch size
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=0
    )
    
    # Optimizer with lower learning rate for fine-tuning
    optimizer = torch.optim.AdamW(model.parameters(), lr=5e-4, weight_decay=0.01)
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    
    # Continue training
    logger.info("Starting continued training with more data...")
    model.train()
    
    num_epochs = 3
    total_loss = 0
    step = 0
    
    for epoch in range(num_epochs):
        logger.info(f"Continued Training Epoch {epoch + 1}/{num_epochs}")
        
        epoch_loss = 0
        progress_bar = tqdm(dataloader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            # Move to GPU
            src = batch['src'].to(device)
            tgt = batch['tgt'].to(device)
            labels = batch['labels'].to(device)
            
            # Forward pass
            outputs = model(src, tgt)
            
            # Calculate loss
            loss = criterion(outputs.view(-1, vocab_size), labels.view(-1))
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            epoch_loss += loss.item()
            step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{epoch_loss / (progress_bar.n + 1):.4f}",
                'gpu_mem': f"{torch.cuda.memory_allocated() / 1e9:.1f}GB" if torch.cuda.is_available() else "N/A"
            })
            
            # Log periodically
            if step % 50 == 0:
                mem_info = f", GPU memory = {torch.cuda.memory_allocated() / 1e9:.1f}GB" if torch.cuda.is_available() else ""
                logger.info(f"Step {step}: loss = {loss.item():.4f}{mem_info}")
        
        logger.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(dataloader):.4f}")
    
    # Save improved model
    model_dir = Path("nunavut_mt/models/improved_gpu")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    improved_model_path = model_dir / "improved_translation_model.pt"
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'model_config': {
            'd_model': 256,
            'nhead': 4,
            'num_layers': 3,
            'max_seq_len': 64
        },
        'tokenizer_path': tokenizer_path,
        'training_samples': len(dataset),
        'continued_training': True
    }, improved_model_path)
    
    logger.info(f"Improved model saved to: {improved_model_path}")
    
    # Test improved model
    test_improved_model(improved_model_path, tokenizer)
    
    return improved_model_path


def test_improved_model(model_path, tokenizer):
    """Test the improved model."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load model
    checkpoint = torch.load(model_path, map_location=device)
    vocab_size = checkpoint['vocab_size']
    
    from train_efficient_gpu import EfficientTranslator
    
    model = EfficientTranslator(
        vocab_size=vocab_size, 
        d_model=256,
        nhead=4,
        num_layers=3,
        max_seq_len=64
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # Test sentences
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "Hello, how are you?",
        "Good morning.",
        "The government is working.",
        "I am happy.",
        "Welcome to Nunavut."
    ]
    
    logger.info("Testing improved model:")
    print("\n" + "="*60)
    print("🚀 IMPROVED GPU MODEL TRANSLATIONS")
    print("="*60)
    
    with torch.no_grad():
        for sentence in test_sentences:
            # Tokenize input
            input_text = f"<en2iu> {sentence}"
            input_tokens = tokenizer.encode_as_ids(input_text)
            
            # Limit length
            if len(input_tokens) > 32:
                input_tokens = input_tokens[:32]
            
            src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
            
            # Improved decoding with temperature
            max_len = 32
            output_tokens = [2]  # Start with BOS
            
            for _ in range(max_len):
                tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
                
                outputs = model(src_tensor, tgt_tensor)
                logits = outputs[0, -1]
                
                # Apply temperature for better sampling
                temperature = 0.8
                logits = logits / temperature
                
                # Sample from top-k
                top_k = 10
                top_logits, top_indices = torch.topk(logits, top_k)
                probs = torch.softmax(top_logits, dim=-1)
                next_token_idx = torch.multinomial(probs, 1).item()
                next_token = top_indices[next_token_idx].item()
                
                if next_token == 3:  # EOS token
                    break
                    
                output_tokens.append(next_token)
            
            # Decode output
            if len(output_tokens) > 1:
                predicted_text = tokenizer.decode_ids(output_tokens[1:])  # Skip BOS
                # Clean up the output
                predicted_text = predicted_text.replace('<en2iu>', '').replace('<iu2en>', '').strip()
            else:
                predicted_text = "[No output generated]"
            
            print(f"EN: {sentence}")
            print(f"IU: {predicted_text}")
            print("-" * 40)


if __name__ == "__main__":
    logger.info("🚀 Starting improved GPU training")
    
    try:
        model_path = train_improved_model()
        logger.info("🎉 Improved training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise
