"""
Bidirectional Machine Translation Model for English-Inuktitut

This module implements a bidirectional translation model using a pre-trained
multilingual transformer (mBART) fine-tuned for English-Inuktitut translation.
"""

import torch
import torch.nn as nn
from transformers import (
    MBartForConditionalGeneration, 
    MBartTokenizer,
    MBartConfig,
    AutoTokenizer,
    AutoModelForSeq2SeqLM
)
import sentencepiece as smp
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging

logger = logging.getLogger(__name__)


class BilingualTokenizer:
    """Custom tokenizer wrapper for bidirectional translation."""
    
    def __init__(self, tokenizer_path: str):
        """
        Initialize the bilingual tokenizer.
        
        Args:
            tokenizer_path: Path to the trained SentencePiece model
        """
        self.sp = smp.SentencePieceProcessor()
        self.sp.load(tokenizer_path)
        
        # Special tokens
        self.pad_token = "<pad>"
        self.unk_token = "<unk>"
        self.bos_token = "<s>"
        self.eos_token = "</s>"
        self.en2iu_token = "<en2iu>"
        self.iu2en_token = "<iu2en>"
        
        # Token IDs
        self.pad_token_id = 0
        self.unk_token_id = 1
        self.bos_token_id = 2
        self.eos_token_id = 3
        self.en2iu_token_id = self.sp.piece_to_id(self.en2iu_token)
        self.iu2en_token_id = self.sp.piece_to_id(self.iu2en_token)
        
        self.vocab_size = self.sp.get_piece_size()
        
    def encode(self, text: str, direction: str = "en2iu") -> List[int]:
        """
        Encode text with direction token.
        
        Args:
            text: Input text to encode
            direction: Translation direction ("en2iu" or "iu2en")
            
        Returns:
            List of token IDs
        """
        # Add direction token
        direction_token = self.en2iu_token if direction == "en2iu" else self.iu2en_token
        prefixed_text = f"{direction_token} {text}"
        
        # Encode with SentencePiece
        token_ids = self.sp.encode_as_ids(prefixed_text)
        
        # Add BOS token
        return [self.bos_token_id] + token_ids
    
    def decode(self, token_ids: List[int], skip_special_tokens: bool = True) -> str:
        """
        Decode token IDs to text.
        
        Args:
            token_ids: List of token IDs
            skip_special_tokens: Whether to skip special tokens
            
        Returns:
            Decoded text
        """
        # Remove BOS/EOS tokens if present
        if token_ids and token_ids[0] == self.bos_token_id:
            token_ids = token_ids[1:]
        if token_ids and token_ids[-1] == self.eos_token_id:
            token_ids = token_ids[:-1]
            
        text = self.sp.decode_ids(token_ids)
        
        if skip_special_tokens:
            # Remove direction tokens
            text = text.replace(self.en2iu_token, "").replace(self.iu2en_token, "")
            text = text.strip()
            
        return text
    
    def batch_encode(self, texts: List[str], direction: str = "en2iu", 
                    max_length: int = 512, padding: bool = True) -> Dict[str, torch.Tensor]:
        """
        Batch encode texts.
        
        Args:
            texts: List of input texts
            direction: Translation direction
            max_length: Maximum sequence length
            padding: Whether to pad sequences
            
        Returns:
            Dictionary with input_ids and attention_mask tensors
        """
        encoded_texts = [self.encode(text, direction) for text in texts]
        
        if padding:
            # Pad sequences
            max_len = min(max_length, max(len(seq) for seq in encoded_texts))
            
            input_ids = []
            attention_mask = []
            
            for seq in encoded_texts:
                # Truncate if too long
                if len(seq) > max_len:
                    seq = seq[:max_len-1] + [self.eos_token_id]
                
                # Pad if too short
                pad_length = max_len - len(seq)
                padded_seq = seq + [self.pad_token_id] * pad_length
                mask = [1] * len(seq) + [0] * pad_length
                
                input_ids.append(padded_seq)
                attention_mask.append(mask)
            
            return {
                'input_ids': torch.tensor(input_ids, dtype=torch.long),
                'attention_mask': torch.tensor(attention_mask, dtype=torch.long)
            }
        else:
            return {'input_ids': encoded_texts}


class BidirectionalMTModel(nn.Module):
    """Bidirectional Machine Translation Model."""
    
    def __init__(self, tokenizer: BilingualTokenizer, model_name: str = "facebook/mbart-large-cc25"):
        """
        Initialize the bidirectional MT model.
        
        Args:
            tokenizer: Custom bilingual tokenizer
            model_name: Pre-trained model name or path
        """
        super().__init__()
        
        self.tokenizer = tokenizer
        
        # Load pre-trained model
        try:
            self.model = MBartForConditionalGeneration.from_pretrained(model_name)
            logger.info(f"Loaded mBART model: {model_name}")
        except:
            # Fallback to a smaller model if mBART-large is not available
            logger.warning(f"Could not load {model_name}, trying mBART-large-50")
            try:
                self.model = MBartForConditionalGeneration.from_pretrained("facebook/mbart-large-50")
            except:
                logger.warning("Could not load mBART, using T5 as fallback")
                self.model = AutoModelForSeq2SeqLM.from_pretrained("t5-base")
        
        # Resize token embeddings to match our vocabulary
        self.model.resize_token_embeddings(tokenizer.vocab_size)
        
        # Store original vocab size for reference
        self.original_vocab_size = self.model.config.vocab_size
        self.new_vocab_size = tokenizer.vocab_size
        
        logger.info(f"Resized vocabulary: {self.original_vocab_size} -> {self.new_vocab_size}")
    
    def forward(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass through the model.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Attention mask
            labels: Target token IDs (for training)
            
        Returns:
            Model outputs
        """
        outputs = self.model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels
        )
        
        return {
            'loss': outputs.loss if labels is not None else None,
            'logits': outputs.logits,
            'hidden_states': outputs.encoder_last_hidden_state if hasattr(outputs, 'encoder_last_hidden_state') else None
        }
    
    def generate(self, input_ids: torch.Tensor, attention_mask: torch.Tensor,
                max_length: int = 512, num_beams: int = 4, 
                early_stopping: bool = True) -> torch.Tensor:
        """
        Generate translations.
        
        Args:
            input_ids: Input token IDs
            attention_mask: Attention mask
            max_length: Maximum generation length
            num_beams: Number of beams for beam search
            early_stopping: Whether to stop early
            
        Returns:
            Generated token IDs
        """
        with torch.no_grad():
            outputs = self.model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                max_length=max_length,
                num_beams=num_beams,
                early_stopping=early_stopping,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                bos_token_id=self.tokenizer.bos_token_id
            )
        
        return outputs
    
    def translate(self, texts: Union[str, List[str]], direction: str = "en2iu",
                 max_length: int = 512, num_beams: int = 4) -> Union[str, List[str]]:
        """
        Translate text(s) in the specified direction.
        
        Args:
            texts: Input text(s) to translate
            direction: Translation direction ("en2iu" or "iu2en")
            max_length: Maximum generation length
            num_beams: Number of beams for beam search
            
        Returns:
            Translated text(s)
        """
        is_single = isinstance(texts, str)
        if is_single:
            texts = [texts]
        
        # Encode inputs
        inputs = self.tokenizer.batch_encode(texts, direction=direction, max_length=max_length)
        
        # Move to device
        device = next(self.parameters()).device
        input_ids = inputs['input_ids'].to(device)
        attention_mask = inputs['attention_mask'].to(device)
        
        # Generate translations
        outputs = self.generate(
            input_ids=input_ids,
            attention_mask=attention_mask,
            max_length=max_length,
            num_beams=num_beams
        )
        
        # Decode outputs
        translations = []
        for output in outputs:
            translation = self.tokenizer.decode(output.cpu().tolist(), skip_special_tokens=True)
            translations.append(translation)
        
        return translations[0] if is_single else translations
    
    def save_pretrained(self, save_directory: str):
        """Save the model and tokenizer."""
        save_path = Path(save_directory)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save the model
        self.model.save_pretrained(save_path)
        
        # Save tokenizer info
        import json
        tokenizer_info = {
            'vocab_size': self.tokenizer.vocab_size,
            'special_tokens': {
                'pad_token': self.tokenizer.pad_token,
                'unk_token': self.tokenizer.unk_token,
                'bos_token': self.tokenizer.bos_token,
                'eos_token': self.tokenizer.eos_token,
                'en2iu_token': self.tokenizer.en2iu_token,
                'iu2en_token': self.tokenizer.iu2en_token
            },
            'special_token_ids': {
                'pad_token_id': self.tokenizer.pad_token_id,
                'unk_token_id': self.tokenizer.unk_token_id,
                'bos_token_id': self.tokenizer.bos_token_id,
                'eos_token_id': self.tokenizer.eos_token_id,
                'en2iu_token_id': self.tokenizer.en2iu_token_id,
                'iu2en_token_id': self.tokenizer.iu2en_token_id
            }
        }
        
        with open(save_path / 'tokenizer_info.json', 'w', encoding='utf-8') as f:
            json.dump(tokenizer_info, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Model saved to {save_directory}")
    
    @classmethod
    def from_pretrained(cls, model_directory: str, tokenizer_path: str):
        """Load a pre-trained model."""
        # Load tokenizer
        tokenizer = BilingualTokenizer(tokenizer_path)
        
        # Create model instance
        model = cls(tokenizer, model_directory)
        
        logger.info(f"Model loaded from {model_directory}")
        return model


def create_model(tokenizer_path: str, pretrained_model: str = "facebook/mbart-large-cc25") -> BidirectionalMTModel:
    """
    Create a bidirectional MT model.
    
    Args:
        tokenizer_path: Path to the trained SentencePiece tokenizer
        pretrained_model: Pre-trained model name
        
    Returns:
        Initialized bidirectional MT model
    """
    tokenizer = BilingualTokenizer(tokenizer_path)
    model = BidirectionalMTModel(tokenizer, pretrained_model)
    
    return model


if __name__ == "__main__":
    # Example usage
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    
    # Create model
    model = create_model(tokenizer_path)
    
    # Test translation
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "The Government of Nunavut is committed to improving education."
    ]
    
    print("Testing English to Inuktitut translation:")
    for sentence in test_sentences:
        translation = model.translate(sentence, direction="en2iu")
        print(f"EN: {sentence}")
        print(f"IU: {translation}")
        print()
    
    print("Model created successfully!")
