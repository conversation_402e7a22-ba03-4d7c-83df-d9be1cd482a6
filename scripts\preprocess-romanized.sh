#!/bin/bash -v

# @file preprocess-romanized.sh
# @brief Run preprocessing for English and romanized Inuktitut (Hansard v3).
#
# <AUTHOR>
#
# Traitement multilingue de textes / Multilingual Text Processing
# Centre de recherche en technologies numériques / Digital Technologies Research Centre
# Conseil national de recherches Canada / National Research Council Canada
# Copyright 2020, Sa Majeste la Reine du Chef du Canada /
# Copyright 2020, Her Majesty in Right of Canada

# USAGE: bash preprocess-romanized.sh
# Final output: truecased data in the $data/tc directory

# Before using, modify the following paths:

# data: output directory to which all preprocessing output will be written
data=OUTPUT_DIRECTORY_ROMANIZED

# rawdata should point to the directory containing train/dev/devtest/test files
# as downloaded with the NH 3.0 release
rawdata=RAW_DATA_DIRECTORY

# mosesdecoder should point to your local mosesscripts directory
# To use the exact version, clone https://github.com/moses-smt/mosesdecoder 
# then switch to: commit b1163966b1a9b4a3d6eec5a54b8bbf5f674a447b 
mosesdecoder=mosesscripts


#The remainder can be left as is.
SRC=en
TRG=iu

model=$data/models
mkdir -p $model

# Convert Inuktitut data from syllabics to romanized (uniconv)
# Reconvert that output to UTF-8 (fixing French characters, for example)
# Pass that through hand-developed additional corrections.
# Write the output to $data/romanized/$prefix.iu
for prefix in train dev-dedup devtest-dedup test-dedup dev devtest test
do
    if [ -f "$data/romanized/$prefix.$TRG" ]; then
	echo "$data/romanized/$prefix.$TRG exists." >&2
    else
	mkdir -p $data/romanized
	uniconv -decode utf-8 -encode Inuktitut-ICI -in $rawdata/$prefix.$TRG -out $data/romanized/$prefix.uniconv.$TRG
	iconv -f ISO-8859-1 -t UTF-8 $data/romanized/$prefix.uniconv.$TRG > $data/romanized/$prefix.iconv.$TRG
	cat $data/romanized/$prefix.iconv.$TRG | perl normalize-apos-romanized.pl > $data/romanized/$prefix.$TRG
    fi
done

# Normalize punctuation; write the output to $data/norm/$prefix.{SRC|TRG}
for prefix in train dev-dedup devtest-dedup test-dedup dev devtest test
do
    mkdir -p $data/norm
    
    if [ -f "$data/norm/$prefix.$SRC" ]; then
        echo "$data/norm/$prefix.$SRC exists." >&2
    else
        cat $rawdata/$prefix.$SRC \
	    | $mosesdecoder/tokenizer/normalize-punctuation.perl -l $SRC > $data/norm/$prefix.$SRC
    fi
    if [ -f "$data/tok/$prefix.$TRG" ]; then
        echo "$data/tok/$prefix.$TRG exists." >&2
    else
        cat $data/romanized/$prefix.$TRG \
            | $mosesdecoder/tokenizer/normalize-punctuation.perl -l $TRG > $data/norm/$prefix.$TRG
    fi
done

# Tokenize data; write the output to $data/tok/$prefix.{SRC|TRG}
for prefix in train dev-dedup devtest-dedup test-dedup dev devtest test
do
    mkdir -p $data/tok

    for LNG in $SRC $TRG
    do
	if [ -f "$data/tok/$prefix.$LNG" ]; then
            echo "$data/tok/$prefix.$LNG exists." >&2
	else
            cat $data/norm/$prefix.$LNG \
		| $mosesdecoder/tokenizer/tokenizer.perl -a -l $LNG > $data/tok/$prefix.$LNG
	fi
    done
done

# Clean training data only
mkdir -p $data/clean
if [ -f "$data/clean/train.$SRC" -a -f "$data/clean/train.$TRG" ]; then
    echo "Data cleaning has already been run." >&2
else
    # clean empty and long sentences, and sentences with high source-target ratio (training corpus only)
    $mosesdecoder/training/clean-corpus-n.perl -ratio 15 $data/tok/train $SRC $TRG $data/clean/train 1 200
fi
python track_removal.py $rawdata/train.id $data/tok/train.en $data/clean/train.en > $data/clean/train.id

# Train a truecaser on the training data only
if [ -f "$model/tc.$SRC" -a -f "$model/tc.$TRG" ]; then
    echo "Truecaser exists." >&2
else
    $mosesdecoder/recaser/train-truecaser.perl -corpus $data/clean/train.$SRC -model $model/tc.$SRC
    $mosesdecoder/recaser/train-truecaser.perl -corpus $data/clean/train.$TRG -model $model/tc.$TRG
fi

# Apply truecaser (cleaned training corpus)
mkdir -p $data/tc
for prefix in train
do
    if [ -f "$data/tc/$prefix.$SRC" -a -f "$data/tc/$prefix.$TRG" ]; then
        echo "Truecaser has been applied (train)." >&2
    else
        $mosesdecoder/recaser/truecase.perl -model $model/tc.$SRC < $data/clean/$prefix.$SRC > $data/tc/$prefix.$SRC
        $mosesdecoder/recaser/truecase.perl -model $model/tc.$TRG < $data/clean/$prefix.$TRG > $data/tc/$prefix.$TRG
    fi
done

# Apply truecaser (dev/test files, not cleaned!)
for prefix in dev-dedup devtest-dedup test-dedup dev devtest test
do
    if [ -f "$data/tc/$prefix.$SRC" -a -f "$data/tc/$prefix.$TRG" ]; then
        echo "Truecaser has been applied ($prefix)." >&2
    else
        $mosesdecoder/recaser/truecase.perl -model $model/tc.$SRC < $data/tok/$prefix.$SRC > $data/tc/$prefix.$SRC
        $mosesdecoder/recaser/truecase.perl -model $model/tc.$TRG < $data/tok/$prefix.$TRG > $data/tc/$prefix.$TRG
    fi
done
