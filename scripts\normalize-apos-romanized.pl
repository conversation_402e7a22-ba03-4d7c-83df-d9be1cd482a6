#!/usr/bin/env perl

# @file normalize-apos-romanized.pl
# @brief Handle encoding issues and apostrophes for romanized Inuktitut.
#
# <AUTHOR>
#
# Traitement multilingue de textes / Multilingual Text Processing
# Centre de recherche en technologies numériques / Digital Technologies Research Centre
# Conseil national de recherches Canada / National Research Council Canada
# Copyright 2020, Sa Majeste la Reine du Chef du Canada /
# Copyright 2020, Her Majesty in Right of Canada

use warnings;
use strict;

while(<STDIN>) {
    # Handle Quotation Marks first
    s/\\u2018\\u2019/“/g;
    s/\\u2019\\u2019/”/g;
    
    # Normalize apostrophes (within words, to MODIFIER LETTER APOSTROPHE U+02BC):
    # MODIFIER LETTER APOSTROPHE U+02BC is NOT split off by Moses tokenizer.
    s/\`/ʼ/g;
    s/\'/ʼ/g;
    s/\\u2019/ʼ/g;
    s/([a-z])\\u2018/$1ʼ/g; # Keep this as a letter if attached to a word.
    s/´/ʼ/g;
    
    # Handle other issues:
    s/\\u2010/-/g; # Manual conversion to hyphen (from hyphen), used as hyphen
    s/\\u2013/–/g; # en dash (handled in Moses scripts)
    s/\\u2014/-/g; # Manual conversion to hyphen (from em dash), used as hyphen
    s/\\u2018/‘/g;
    s/\\u2022/•/g;
    s/\\u2026/…/g;
    s/\\u2030/‰/g;
    s/\\u2034/‴/g;
    s/\\u2044/⁄/g;
    s/\\u201c/“/g;
    s/\\u201d/”/g;
    s/\\u2212/-/g; # Manual conversion to hyphen (from minus sign), used as hyphen
    s/\\u2217/∗/g;
    s/\\u221a/√/g;
    s/\\u0178/Ÿ/g;
    s/\\u02c6/^/g; # Converted from ˆ
    s/\\u02dc/~/g; # Converted from ˜
    
    # Data annotations:
    s/\\uf0b7/-/g; # MS Word first-level bullet point, converting to hyphen
    s/\\uf03e/>/g; # Annotation

    # Other unhandled syllabics
    s/\\u141e/ᐞ/g; # Glottal stop
    s/\\u157d/ᕽ/g; # HK
    s/\\u1586/ᖆ/g; # TLHE
    s/\\u14a2/ᒢ/g; # SAYISI TH
    s/\\u143a/ᐺ/g; # PWE
    s/\\u1402/ᐂ/g; # AAI
    s/\\u140c/ᐌ/g; # WE
    s/\\u143a/ᐺ/g; # PWE
    s/\\u144a/ᑊ/g; # WEST-CREE P
    s/\\u1457/ᑗ/g; # TWE
    s/\\u1467/ᑧ/g; # TTE
    s/\\u146c/ᑬ/g; # KAAI
    s/\\u1484/ᒄ/g; # KW
    s/\\u1492/ᒒ/g; # CWE
    s/\\u14ac/ᒬ/g; # MWE
    s/\\u14d9/ᓙ/g; # Y-CREE LOO
    s/\\u14dc/ᓜ/g; # LWE
    s/\\u14eb/ᓫ/g; # WEST-CREE L
    s/\\u1506/ᔆ/g; # ATHAPASCAN S
    s/\\u152f/ᔯ/g; # YWE
    s/\\u1551/ᕑ/g; # WEST-CREE R
    s/\\u155b/ᕛ/g; # FWAA
    s/\\u155e/ᕞ/g; # THE

    print $_;
}
