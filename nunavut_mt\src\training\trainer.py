"""
Training infrastructure for bidirectional machine translation.

This module provides training loops, evaluation metrics, and experiment tracking
for the English-Inuktitut bidirectional translation model.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from pathlib import Path
import time
import json
import logging
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MTTrainer:
    """Trainer for machine translation models."""
    
    def __init__(self, 
                 model,
                 tokenizer,
                 train_loader: DataLoader,
                 val_loader: DataLoader,
                 optimizer,
                 scheduler=None,
                 device: str = "cuda" if torch.cuda.is_available() else "cpu",
                 save_dir: str = "nunavut_mt/checkpoints",
                 log_steps: int = 100,
                 eval_steps: int = 1000,
                 save_steps: int = 2000,
                 max_grad_norm: float = 1.0):
        """
        Initialize the trainer.
        
        Args:
            model: The MT model to train
            tokenizer: Tokenizer instance
            train_loader: Training data loader
            val_loader: Validation data loader
            optimizer: Optimizer instance
            scheduler: Learning rate scheduler (optional)
            device: Device to train on
            save_dir: Directory to save checkpoints
            log_steps: Steps between logging
            eval_steps: Steps between evaluations
            save_steps: Steps between saving checkpoints
            max_grad_norm: Maximum gradient norm for clipping
        """
        self.model = model
        self.tokenizer = tokenizer
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.device = device
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.log_steps = log_steps
        self.eval_steps = eval_steps
        self.save_steps = save_steps
        self.max_grad_norm = max_grad_norm
        
        # Training state
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        self.training_history = []
        
        # Move model to device
        self.model.to(device)
        
        logger.info(f"Trainer initialized on device: {device}")
        logger.info(f"Training batches: {len(train_loader)}")
        logger.info(f"Validation batches: {len(val_loader)}")
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(self.train_loader, desc=f"Epoch {self.epoch}")
        
        for batch in progress_bar:
            # Move batch to device
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            labels = batch['labels'].to(self.device)
            
            # Forward pass
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs['loss']
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            if self.max_grad_norm > 0:
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
            
            self.optimizer.step()
            
            if self.scheduler:
                self.scheduler.step()
            
            # Update metrics
            total_loss += loss.item()
            num_batches += 1
            self.global_step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{total_loss/num_batches:.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.2e}"
            })
            
            # Logging
            if self.global_step % self.log_steps == 0:
                logger.info(f"Step {self.global_step}: loss={loss.item():.4f}, lr={self.optimizer.param_groups[0]['lr']:.2e}")
            
            # Evaluation
            if self.global_step % self.eval_steps == 0:
                val_metrics = self.evaluate()
                logger.info(f"Validation at step {self.global_step}: {val_metrics}")
                
                # Save best model
                if val_metrics['val_loss'] < self.best_val_loss:
                    self.best_val_loss = val_metrics['val_loss']
                    self.save_checkpoint(is_best=True)
                    logger.info(f"New best model saved with val_loss={self.best_val_loss:.4f}")
            
            # Save checkpoint
            if self.global_step % self.save_steps == 0:
                self.save_checkpoint()
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return {'train_loss': avg_loss}
    
    def evaluate(self) -> Dict[str, float]:
        """Evaluate the model on validation set."""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Evaluating"):
                # Move batch to device
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # Forward pass
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs['loss']
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        # Switch back to training mode
        self.model.train()
        
        return {'val_loss': avg_loss}
    
    def train(self, num_epochs: int):
        """Train the model for specified number of epochs."""
        logger.info(f"Starting training for {num_epochs} epochs")
        
        for epoch in range(num_epochs):
            self.epoch = epoch
            
            # Train epoch
            train_metrics = self.train_epoch()
            
            # Evaluate
            val_metrics = self.evaluate()
            
            # Combine metrics
            epoch_metrics = {**train_metrics, **val_metrics, 'epoch': epoch, 'global_step': self.global_step}
            self.training_history.append(epoch_metrics)
            
            logger.info(f"Epoch {epoch} completed: {epoch_metrics}")
            
            # Save training history
            self.save_training_history()
        
        logger.info("Training completed!")
        
        # Save final model
        self.save_checkpoint(is_final=True)
    
    def save_checkpoint(self, is_best: bool = False, is_final: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'global_step': self.global_step,
            'epoch': self.epoch,
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history
        }
        
        if is_best:
            checkpoint_path = self.save_dir / "best_model.pt"
        elif is_final:
            checkpoint_path = self.save_dir / "final_model.pt"
        else:
            checkpoint_path = self.save_dir / f"checkpoint_step_{self.global_step}.pt"
        
        torch.save(checkpoint, checkpoint_path)
        logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.global_step = checkpoint['global_step']
        self.epoch = checkpoint['epoch']
        self.best_val_loss = checkpoint['best_val_loss']
        self.training_history = checkpoint['training_history']
        
        logger.info(f"Checkpoint loaded: {checkpoint_path}")
        logger.info(f"Resumed from step {self.global_step}, epoch {self.epoch}")
    
    def save_training_history(self):
        """Save training history to JSON."""
        history_path = self.save_dir / "training_history.json"
        with open(history_path, 'w', encoding='utf-8') as f:
            json.dump(self.training_history, f, indent=2)


class SimpleEvaluator:
    """Simple evaluator for translation quality."""
    
    def __init__(self, model, tokenizer, device: str = "cuda" if torch.cuda.is_available() else "cpu"):
        """
        Initialize evaluator.
        
        Args:
            model: Trained MT model
            tokenizer: Tokenizer instance
            device: Device to run evaluation on
        """
        self.model = model
        self.tokenizer = tokenizer
        self.device = device
        self.model.to(device)
        self.model.eval()
    
    def translate_batch(self, texts: List[str], direction: str = "en2iu", max_length: int = 512) -> List[str]:
        """Translate a batch of texts."""
        with torch.no_grad():
            # Encode inputs
            inputs = self.tokenizer.batch_encode(texts, direction=direction, max_length=max_length)
            input_ids = inputs['input_ids'].to(self.device)
            attention_mask = inputs['attention_mask'].to(self.device)
            
            # Generate translations
            outputs = self.model.generate(
                input_ids=input_ids,
                attention_mask=attention_mask,
                max_length=max_length,
                num_beams=4,
                early_stopping=True
            )
            
            # Decode outputs
            translations = []
            for output in outputs:
                translation = self.tokenizer.decode(output.cpu().tolist(), skip_special_tokens=True)
                translations.append(translation)
            
            return translations
    
    def evaluate_samples(self, source_texts: List[str], reference_texts: List[str], 
                        direction: str = "en2iu") -> Dict[str, float]:
        """Evaluate translation quality on sample texts."""
        translations = self.translate_batch(source_texts, direction)
        
        # Simple metrics (can be extended with BLEU, chrF, etc.)
        metrics = {
            'num_samples': len(translations),
            'avg_translation_length': sum(len(t.split()) for t in translations) / len(translations),
            'avg_reference_length': sum(len(r.split()) for r in reference_texts) / len(reference_texts)
        }
        
        # Print some examples
        logger.info(f"Sample translations ({direction}):")
        for i, (src, ref, trans) in enumerate(zip(source_texts[:3], reference_texts[:3], translations[:3])):
            logger.info(f"  {i+1}. Source: {src}")
            logger.info(f"     Reference: {ref}")
            logger.info(f"     Translation: {trans}")
            logger.info("")
        
        return metrics


def create_optimizer_and_scheduler(model, learning_rate: float = 5e-5, 
                                 warmup_steps: int = 1000, 
                                 total_steps: int = 10000):
    """Create optimizer and learning rate scheduler."""
    
    # AdamW optimizer
    optimizer = torch.optim.AdamW(
        model.parameters(),
        lr=learning_rate,
        betas=(0.9, 0.999),
        eps=1e-8,
        weight_decay=0.01
    )
    
    # Linear warmup + cosine decay scheduler
    def lr_lambda(step):
        if step < warmup_steps:
            return step / warmup_steps
        else:
            progress = (step - warmup_steps) / (total_steps - warmup_steps)
            return 0.5 * (1 + torch.cos(torch.tensor(progress * 3.14159)))
    
    scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    return optimizer, scheduler


if __name__ == "__main__":
    # Example usage would go here
    print("Training infrastructure created successfully!")
    print("Key components:")
    print("- MTTrainer: Main training loop with checkpointing")
    print("- SimpleEvaluator: Translation quality evaluation")
    print("- Optimizer/Scheduler creation utilities")
    print("- Comprehensive logging and progress tracking")
