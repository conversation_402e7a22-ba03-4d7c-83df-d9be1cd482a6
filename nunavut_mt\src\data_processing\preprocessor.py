"""
Data Preprocessing Module for Nunavut Hansard Corpus

This module handles cleaning, normalization, and preparation of the parallel corpus
for machine translation training.
"""

import re
import os
import subprocess
from pathlib import Path
from typing import List, Tuple, Optional, Dict
import unicodedata
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CorpusPreprocessor:
    """Preprocessor for the Nunavut Hansard parallel corpus."""
    
    def __init__(self, corpus_dir: str, output_dir: str):
        """
        Initialize the preprocessor.
        
        Args:
            corpus_dir: Path to the original corpus directory
            output_dir: Path to save processed data
        """
        self.corpus_dir = Path(corpus_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Paths to original data
        self.split_dir = self.corpus_dir / "split"
        self.scripts_dir = self.corpus_dir / "scripts"
        
    def normalize_text(self, text: str, language: str = "en") -> str:
        """
        Normalize text for the given language.
        
        Args:
            text: Input text to normalize
            language: Language code ("en" or "iu")
            
        Returns:
            Normalized text
        """
        if not text.strip():
            return text
            
        # Basic normalization
        text = unicodedata.normalize('NFC', text)
        
        if language == "en":
            # English-specific normalization
            # Convert to lowercase for consistency (can be reverted later if needed)
            # text = text.lower()  # Commented out to preserve case for now
            
            # Normalize quotes and apostrophes
            text = re.sub(r'["""]', '"', text)
            text = re.sub(r"[''']", "'", text)
            
            # Normalize whitespace
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
        elif language == "iu":
            # Inuktitut-specific normalization
            # Keep syllabics as-is, but normalize punctuation and whitespace
            
            # Normalize quotes and apostrophes (similar to English)
            text = re.sub(r'["""]', '"', text)
            text = re.sub(r"[''']", "'", text)
            
            # Normalize whitespace
            text = re.sub(r'\s+', ' ', text)
            text = text.strip()
            
        return text
    
    def apply_corpus_scripts(self, input_file: str, output_file: str, language: str):
        """
        Apply the corpus-provided normalization scripts.
        
        Args:
            input_file: Path to input file
            output_file: Path to output file
            language: Language code ("en" or "iu")
        """
        if language == "iu" and (self.scripts_dir / "normalize-iu-spelling.pl").exists():
            # Apply Inuktitut spelling normalization
            cmd = [
                "perl",
                str(self.scripts_dir / "normalize-iu-spelling.pl"),
                input_file
            ]
            
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    subprocess.run(cmd, stdout=f, check=True, encoding='utf-8')
                logger.info(f"Applied Inuktitut normalization to {input_file}")
            except subprocess.CalledProcessError as e:
                logger.warning(f"Failed to apply Inuktitut normalization: {e}")
                # Fallback: just copy the file
                with open(input_file, 'r', encoding='utf-8') as fin:
                    with open(output_file, 'w', encoding='utf-8') as fout:
                        fout.write(fin.read())
        else:
            # For English or if script not available, just copy
            with open(input_file, 'r', encoding='utf-8') as fin:
                with open(output_file, 'w', encoding='utf-8') as fout:
                    fout.write(fin.read())
    
    def filter_sentence_pairs(self, en_sentences: List[str], iu_sentences: List[str], 
                            ids: List[str]) -> Tuple[List[str], List[str], List[str]]:
        """
        Filter sentence pairs based on quality criteria.
        
        Args:
            en_sentences: List of English sentences
            iu_sentences: List of Inuktitut sentences
            ids: List of sentence IDs
            
        Returns:
            Filtered lists of sentences and IDs
        """
        filtered_en = []
        filtered_iu = []
        filtered_ids = []
        
        for en, iu, id_str in zip(en_sentences, iu_sentences, ids):
            # Skip empty sentences
            if not en.strip() or not iu.strip():
                continue
                
            # Skip very short sentences (likely headers or artifacts)
            if len(en.split()) < 2 or len(iu.split()) < 2:
                continue
                
            # Skip sentences with extreme length ratios
            en_len = len(en.split())
            iu_len = len(iu.split())
            ratio = iu_len / en_len if en_len > 0 else float('inf')
            
            if ratio < 0.1 or ratio > 10.0:  # Very conservative filtering
                continue
                
            # Skip sentences that are mostly numbers or punctuation
            en_alpha_ratio = sum(c.isalpha() for c in en) / len(en) if en else 0
            iu_alpha_ratio = sum(c.isalpha() or '\u1400' <= c <= '\u167F' for c in iu) / len(iu) if iu else 0
            
            if en_alpha_ratio < 0.5 or iu_alpha_ratio < 0.5:
                continue
                
            filtered_en.append(en)
            filtered_iu.append(iu)
            filtered_ids.append(id_str)
        
        logger.info(f"Filtered {len(en_sentences)} -> {len(filtered_en)} sentence pairs")
        return filtered_en, filtered_iu, filtered_ids
    
    def process_split(self, split_name: str, apply_filtering: bool = True) -> Dict[str, int]:
        """
        Process a single data split.
        
        Args:
            split_name: Name of the split (e.g., "train", "dev-dedup")
            apply_filtering: Whether to apply quality filtering
            
        Returns:
            Dictionary with processing statistics
        """
        logger.info(f"Processing {split_name} split...")
        
        # Load original data
        en_file = self.split_dir / f"{split_name}.en"
        iu_file = self.split_dir / f"{split_name}.iu"
        id_file = self.split_dir / f"{split_name}.id"
        
        if not all(f.exists() for f in [en_file, iu_file, id_file]):
            logger.warning(f"Missing files for {split_name} split")
            return {}
        
        # Read files
        with open(en_file, 'r', encoding='utf-8') as f:
            en_sentences = [line.strip() for line in f]
            
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_sentences = [line.strip() for line in f]
            
        with open(id_file, 'r', encoding='utf-8') as f:
            ids = [line.strip() for line in f]
        
        original_count = len(en_sentences)
        
        # Normalize text
        en_sentences = [self.normalize_text(sent, "en") for sent in en_sentences]
        iu_sentences = [self.normalize_text(sent, "iu") for sent in iu_sentences]
        
        # Apply filtering if requested
        if apply_filtering:
            en_sentences, iu_sentences, ids = self.filter_sentence_pairs(
                en_sentences, iu_sentences, ids
            )
        
        # Save processed data
        output_en = self.output_dir / f"{split_name}.en"
        output_iu = self.output_dir / f"{split_name}.iu"
        output_id = self.output_dir / f"{split_name}.id"
        
        with open(output_en, 'w', encoding='utf-8') as f:
            f.write('\n'.join(en_sentences) + '\n')
            
        with open(output_iu, 'w', encoding='utf-8') as f:
            f.write('\n'.join(iu_sentences) + '\n')
            
        with open(output_id, 'w', encoding='utf-8') as f:
            f.write('\n'.join(ids) + '\n')
        
        stats = {
            'original_count': original_count,
            'processed_count': len(en_sentences),
            'filtered_count': original_count - len(en_sentences)
        }
        
        logger.info(f"Processed {split_name}: {original_count} -> {len(en_sentences)} pairs")
        return stats
    
    def process_all_splits(self, apply_filtering: bool = True) -> Dict[str, Dict[str, int]]:
        """
        Process all available data splits.
        
        Args:
            apply_filtering: Whether to apply quality filtering
            
        Returns:
            Dictionary with processing statistics for all splits
        """
        splits = ["train", "dev-dedup", "devtest-dedup", "test-dedup"]
        all_stats = {}
        
        for split in splits:
            try:
                stats = self.process_split(split, apply_filtering)
                if stats:
                    all_stats[split] = stats
            except Exception as e:
                logger.error(f"Error processing {split}: {e}")
                continue
        
        # Save processing statistics
        import json
        stats_file = self.output_dir / "preprocessing_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(all_stats, f, indent=2)
        
        logger.info(f"Processing complete. Statistics saved to {stats_file}")
        return all_stats
    
    def create_combined_corpus(self):
        """Create a combined corpus file for tokenizer training."""
        combined_file = self.output_dir / "combined_corpus.txt"
        
        with open(combined_file, 'w', encoding='utf-8') as fout:
            # Add English data
            for split in ["train"]:  # Only use training data for tokenizer
                en_file = self.output_dir / f"{split}.en"
                if en_file.exists():
                    with open(en_file, 'r', encoding='utf-8') as fin:
                        for line in fin:
                            if line.strip():
                                fout.write(line)
            
            # Add Inuktitut data
            for split in ["train"]:
                iu_file = self.output_dir / f"{split}.iu"
                if iu_file.exists():
                    with open(iu_file, 'r', encoding='utf-8') as fin:
                        for line in fin:
                            if line.strip():
                                fout.write(line)
        
        logger.info(f"Combined corpus created: {combined_file}")
        return combined_file


if __name__ == "__main__":
    # Example usage
    preprocessor = CorpusPreprocessor(
        corpus_dir=".",  # Current directory
        output_dir="nunavut_mt/data/processed"
    )
    
    # Process all splits
    stats = preprocessor.process_all_splits(apply_filtering=True)
    
    # Create combined corpus for tokenizer training
    preprocessor.create_combined_corpus()
    
    print("Preprocessing complete!")
    for split, split_stats in stats.items():
        print(f"{split}: {split_stats['original_count']} -> {split_stats['processed_count']} pairs")
