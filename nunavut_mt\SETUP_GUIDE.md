# Environment Setup Guide for Nunavut MT

This guide will help you set up a proper Python environment for the Nunavut English-Inuktitut Machine Translation system.

## 🚀 Quick Setup (Recommended)

### Option 1: Automated Setup Scripts

#### Windows (Command Prompt)
```cmd
cd nunavut_mt
setup_env.bat
```

#### Windows (PowerShell)
```powershell
cd nunavut_mt
.\setup_env.ps1
```

#### Linux/macOS
```bash
cd nunavut_mt
chmod +x setup_env.sh
./setup_env.sh
```

### Option 2: Manual Setup

#### Using Conda (Recommended)
```bash
# Create environment from YAML file
conda env create -f environment.yml

# Activate environment
conda activate nunavut-mt

# Verify installation
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
```

#### Using pip + venv
```bash
# Create virtual environment
python -m venv nunavut_mt_env

# Activate environment
# On Windows:
nunavut_mt_env\Scripts\activate
# On Linux/macOS:
source nunavut_mt_env/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install PyTorch (CPU version for compatibility)
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu

# Install other requirements
pip install -r requirements.txt
```

## 🔧 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher (3.9 recommended)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 5GB free space
- **OS**: Windows 10+, macOS 10.14+, or Linux

### For GPU Training (Optional)
- **CUDA**: 11.7 or 11.8
- **GPU Memory**: 8GB+ VRAM recommended
- **GPU**: NVIDIA GTX 1080 or better

## 📦 Key Dependencies

### Core ML Libraries
- **PyTorch 2.0.1**: Deep learning framework
- **Transformers 4.30.2**: Hugging Face transformers library
- **SentencePiece 0.1.99**: Tokenization library
- **Datasets 2.12.0**: Dataset processing

### Evaluation & Metrics
- **SacreBLEU 2.3.1**: BLEU score computation
- **Evaluate 0.4.0**: Evaluation framework

### Data Processing
- **NumPy 1.24.3**: Numerical computing
- **Pandas 2.0.3**: Data manipulation
- **Scikit-learn 1.3.0**: Machine learning utilities

## 🧪 Testing Your Installation

### 1. Basic Import Test
```python
# Test core imports
import torch
import transformers
import sentencepiece
import numpy as np
import pandas as pd

print("✅ All core libraries imported successfully!")
print(f"PyTorch version: {torch.__version__}")
print(f"Transformers version: {transformers.__version__}")
```

### 2. Tokenizer Test
```bash
python nunavut_mt/test_tokenizer.py
```

### 3. Translation System Test
```bash
python nunavut_mt/translate_cli.py --text "Hello, world!" --direction en2iu
```

### 4. Full Pipeline Test
```bash
python nunavut_mt/src/inference/translator.py
```

## 🐛 Troubleshooting

### Common Issues

#### 1. "No module named 'torch'"
**Solution**: Install PyTorch first
```bash
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
```

#### 2. "CUDA out of memory" (during training)
**Solutions**:
- Reduce batch size: `--batch_size 4`
- Use CPU: `--device cpu`
- Use gradient accumulation

#### 3. "UnicodeEncodeError" on Windows
**Solution**: Set environment variable
```cmd
set PYTHONIOENCODING=utf-8
```

#### 4. "Permission denied" on Linux/macOS
**Solution**: Make scripts executable
```bash
chmod +x setup_env.sh
```

#### 5. Conda environment conflicts
**Solution**: Create clean environment
```bash
conda deactivate
conda env remove -n nunavut-mt
conda env create -f environment.yml
```

### Version Compatibility Issues

If you encounter version conflicts, try this minimal setup:

```bash
# Create fresh environment
python -m venv nunavut_mt_minimal
source nunavut_mt_minimal/bin/activate  # Linux/macOS
# OR
nunavut_mt_minimal\Scripts\activate     # Windows

# Install minimal requirements
pip install torch transformers sentencepiece numpy pandas tqdm
```

## 🚀 GPU Setup (Optional)

### For NVIDIA GPUs with CUDA

#### Check CUDA Version
```bash
nvidia-smi
nvcc --version
```

#### Install GPU PyTorch
```bash
# For CUDA 11.7
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu117

# For CUDA 11.8
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cu118
```

#### Verify GPU Setup
```python
import torch
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA devices: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"Current device: {torch.cuda.get_device_name()}")
```

## 📝 Environment Activation

### Every Time You Use the System

#### With Conda
```bash
conda activate nunavut-mt
```

#### With venv
```bash
# Windows
nunavut_mt_env\Scripts\activate

# Linux/macOS
source nunavut_mt_env/bin/activate
```

### Verify Environment is Active
```bash
which python  # Should point to your environment
python -c "import torch; print('Environment ready!')"
```

## 🔄 Updating Dependencies

### Update All Packages
```bash
# With conda
conda update --all

# With pip
pip install --upgrade -r requirements.txt
```

### Update Specific Package
```bash
pip install --upgrade transformers
```

## 📊 Performance Optimization

### For CPU Training
```bash
export OMP_NUM_THREADS=4
export MKL_NUM_THREADS=4
```

### For GPU Training
```bash
export CUDA_VISIBLE_DEVICES=0  # Use first GPU only
```

## ✅ Verification Checklist

After setup, verify these work:

- [ ] `python --version` shows 3.8+
- [ ] `python -c "import torch"` works
- [ ] `python -c "import transformers"` works
- [ ] `python -c "import sentencepiece"` works
- [ ] `python nunavut_mt/test_tokenizer.py` runs successfully
- [ ] `python nunavut_mt/translate_cli.py --text "Hello" --direction en2iu` works

## 🆘 Getting Help

If you encounter issues:

1. **Check the error message** carefully
2. **Verify Python version**: `python --version`
3. **Check environment activation**: `which python`
4. **Try minimal installation** first
5. **Check system requirements** (RAM, storage)

### Common Commands for Debugging
```bash
# Check installed packages
pip list

# Check environment info
conda info --envs  # For conda
pip show torch transformers  # For pip

# Test imports one by one
python -c "import torch; print('PyTorch OK')"
python -c "import transformers; print('Transformers OK')"
python -c "import sentencepiece; print('SentencePiece OK')"
```

---

**Ready to start?** Run the setup script for your platform and then test with:
```bash
python nunavut_mt/translate_cli.py --text "Thank you, Mr. Speaker." --direction en2iu
```
