# PowerShell setup script for Nunavut MT environment on Windows

Write-Host "========================================" -ForegroundColor Green
Write-Host "Setting up Nunavut MT Environment" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Function to test if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if conda is available
if (Test-Command "conda") {
    Write-Host "Step 1: Creating conda environment..." -ForegroundColor Yellow
    
    try {
        conda env create -f environment.yml
        
        Write-Host "Step 2: Activating environment..." -ForegroundColor Yellow
        conda activate nunavut-mt
        
        Write-Host "Step 3: Verifying installation..." -ForegroundColor Yellow
        python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
        python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
        python -c "import sentencepiece; print('SentencePiece: OK')"
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Environment setup completed successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "To activate the environment, run:" -ForegroundColor Cyan
        Write-Host "  conda activate nunavut-mt" -ForegroundColor White
        Write-Host ""
        Write-Host "To test the system, run:" -ForegroundColor Cyan
        Write-Host "  python nunavut_mt/translate_cli.py --text `"Hello`" --direction en2iu" -ForegroundColor White
        Write-Host ""
    }
    catch {
        Write-Host "ERROR: Failed to create conda environment" -ForegroundColor Red
        Write-Host "Trying alternative pip installation..." -ForegroundColor Yellow
        $usePip = $true
    }
}
else {
    Write-Host "Conda not found. Setting up with pip..." -ForegroundColor Yellow
    $usePip = $true
}

if ($usePip) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "Alternative: Setting up with pip" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    
    # Check Python version
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Python not found. Please install Python 3.8+ first." -ForegroundColor Red
        Write-Host "Download from: https://www.python.org/downloads/" -ForegroundColor Cyan
        exit 1
    }
    
    Write-Host "Found: $pythonVersion" -ForegroundColor Green
    
    Write-Host "Step 1: Creating virtual environment..." -ForegroundColor Yellow
    python -m venv nunavut_mt_env
    
    Write-Host "Step 2: Activating virtual environment..." -ForegroundColor Yellow
    & "nunavut_mt_env\Scripts\Activate.ps1"
    
    Write-Host "Step 3: Upgrading pip..." -ForegroundColor Yellow
    python -m pip install --upgrade pip
    
    Write-Host "Step 4: Installing PyTorch (CPU version for compatibility)..." -ForegroundColor Yellow
    pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
    
    Write-Host "Step 5: Installing other requirements..." -ForegroundColor Yellow
    pip install -r requirements.txt
    
    Write-Host "Step 6: Verifying installation..." -ForegroundColor Yellow
    python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
    python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
    python -c "import sentencepiece; print('SentencePiece: OK')"
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Pip environment setup completed!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "To activate the environment, run:" -ForegroundColor Cyan
    Write-Host "  nunavut_mt_env\Scripts\Activate.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "To test the system, run:" -ForegroundColor Cyan
    Write-Host "  python nunavut_mt/translate_cli.py --text `"Hello`" --direction en2iu" -ForegroundColor White
    Write-Host ""
}

Write-Host "Setup complete! Check above for any errors." -ForegroundColor Green

# Keep window open
Read-Host "Press Enter to continue..."
