#!/usr/bin/env perl

# @file normalize-apos-syllabics.pl
# @brief Handle apostrophes and quotation marks for Inuktitut syllabics.
#
# <AUTHOR>
#
# Traitement multilingue de textes / Multilingual Text Processing
# Centre de recherche en technologies numériques / Digital Technologies Research Centre
# Conseil national de recherches Canada / National Research Council Canada
# Copyright 2020, Sa Majeste la Reine du Chef du Canada /
# Copyright 2020, Her Majesty in Right of Canada

use warnings;
use strict;

while(<STDIN>) {
    # Quotation marks first
    s/‘’/“/g;
    s/’’/”/g;
    
    # Normalize apostrophes (within words, to MODIFIER LETTER APOSTROPHE U+0027):
    s/\`/ʼ/g;
    s/\ʼ/ʼ/g;
    s/\'/ʼ/g;
    s/’/ʼ/g;
    s/´/ʼ/g;

    print $_;
}
