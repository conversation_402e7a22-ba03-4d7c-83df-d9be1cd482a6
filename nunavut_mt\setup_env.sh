#!/bin/bash
# Setup script for Nunavut MT environment on Linux/macOS

set -e  # Exit on any error

echo "========================================"
echo "Setting up Nunavut MT Environment"
echo "========================================"

# Check if conda is available
if command -v conda &> /dev/null; then
    echo "Step 1: Creating conda environment..."
    conda env create -f environment.yml
    
    echo "Step 2: Activating environment..."
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate nunavut-mt
    
    echo "Step 3: Verifying installation..."
    python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
    python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
    python -c "import sentencepiece; print('SentencePiece: OK')"
    
    echo ""
    echo "========================================"
    echo "Environment setup completed successfully!"
    echo "========================================"
    echo ""
    echo "To activate the environment, run:"
    echo "  conda activate nunavut-mt"
    echo ""
    echo "To test the system, run:"
    echo "  python nunavut_mt/translate_cli.py --text \"Hello\" --direction en2iu"
    echo ""
    
else
    echo "Conda not found. Setting up with pip..."
    
    # Check Python version
    python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
    required_version="3.8"
    
    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
        echo "ERROR: Python 3.8+ required. Found: $python_version"
        exit 1
    fi
    
    echo "Step 1: Creating virtual environment..."
    python3 -m venv nunavut_mt_env
    
    echo "Step 2: Activating virtual environment..."
    source nunavut_mt_env/bin/activate
    
    echo "Step 3: Upgrading pip..."
    pip install --upgrade pip
    
    echo "Step 4: Installing PyTorch (CPU version)..."
    pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
    
    echo "Step 5: Installing other requirements..."
    pip install -r requirements.txt
    
    echo "Step 6: Verifying installation..."
    python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
    python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
    python -c "import sentencepiece; print('SentencePiece: OK')"
    
    echo ""
    echo "========================================"
    echo "Pip environment setup completed!"
    echo "========================================"
    echo ""
    echo "To activate the environment, run:"
    echo "  source nunavut_mt_env/bin/activate"
    echo ""
    echo "To test the system, run:"
    echo "  python nunavut_mt/translate_cli.py --text \"Hello\" --direction en2iu"
    echo ""
fi

echo "Setup complete!"
