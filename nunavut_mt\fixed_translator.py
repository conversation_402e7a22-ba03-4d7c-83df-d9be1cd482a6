#!/usr/bin/env python3
"""
Fixed translator that avoids immediate EOS generation.
"""

import torch
import sentencepiece as spm
from pathlib import Path
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def translate_with_fixed_generation(model, tokenizer, text, direction, device, min_length=3):
    """Translate with improved generation that avoids immediate EOS."""
    
    try:
        # Prepare input
        if direction == "en2iu":
            input_text = f"<en2iu> {text}"
        else:
            input_text = f"<iu2en> {text}"
        
        # Tokenize
        input_tokens = tokenizer.encode_as_ids(input_text)
        if len(input_tokens) > 32:
            input_tokens = input_tokens[:32]
        
        src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
        
        # Generate with EOS avoidance
        max_len = 20
        output_tokens = [2]  # BOS token
        eos_token = 3
        
        for step in range(max_len):
            tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
            
            outputs = model(src_tensor, tgt_tensor)
            logits = outputs[0, -1]
            
            # Avoid EOS for first few tokens
            if step < min_length:
                logits[eos_token] = -float('inf')  # Block EOS
            
            # Use temperature and top-k sampling
            temperature = 0.8
            top_k = 20
            
            # Apply temperature
            logits = logits / temperature
            
            # Get top-k tokens
            top_logits, top_indices = torch.topk(logits, top_k)
            probs = torch.softmax(top_logits, dim=-1)
            
            # Sample from top-k
            next_token_idx = torch.multinomial(probs, 1).item()
            next_token = top_indices[next_token_idx].item()
            
            if next_token == eos_token and step >= min_length:
                break
            
            output_tokens.append(next_token)
        
        # Decode output
        if len(output_tokens) > 1:
            result = tokenizer.decode_ids(output_tokens[1:])  # Skip BOS
            # Clean up
            result = result.replace('<en2iu>', '').replace('<iu2en>', '').strip()
            result = result.replace('<unk>', '').replace('<s>', '').replace('</s>', '').strip()
            return result if result else "[Empty output]"
        else:
            return "[No output generated]"
            
    except Exception as e:
        logger.error(f"Translation failed: {e}")
        return f"[Error: {e}]"


def test_fixed_translator():
    """Test the fixed translator."""
    
    print("🔧 Testing Fixed Neural Translator")
    print("=" * 50)
    
    # Load model
    model_path = "nunavut_mt/models/improved_gpu/improved_translation_model.pt"
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load tokenizer
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    from train_efficient_gpu import EfficientTranslator
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']
    
    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f"✅ Model loaded successfully!")
    
    # Test sentences
    test_sentences = [
        "Thank you",
        "Hello",
        "Good morning",
        "Mr. Speaker",
        "The government",
        "I am happy",
        "Welcome",
        "How are you?",
        "Good evening",
        "See you later"
    ]
    
    print("\n🚀 Fixed Translation Results:")
    print("=" * 60)
    
    success_count = 0
    
    with torch.no_grad():
        for i, sentence in enumerate(test_sentences, 1):
            print(f"\n{i:2d}. EN: {sentence}")
            
            # Translate EN → IU
            result = translate_with_fixed_generation(
                model, tokenizer, sentence, "en2iu", device, min_length=2
            )
            
            print(f"    IU: {result}")
            
            # Check if we got meaningful output
            if result and result not in ["[Empty output]", "[No output generated]"] and len(result) > 1:
                success_count += 1
                print(f"    ✅ Success")
            else:
                print(f"    ⚠️  Needs improvement")
    
    print(f"\n📊 Results Summary:")
    print(f"   - Successful translations: {success_count}/{len(test_sentences)}")
    print(f"   - Success rate: {success_count/len(test_sentences)*100:.1f}%")
    
    if success_count > len(test_sentences) * 0.5:
        print(f"\n🎉 Great! The fixed translator is working much better!")
    elif success_count > 0:
        print(f"\n👍 Good progress! The model is generating output.")
    else:
        print(f"\n🔧 Still needs work, but the model architecture is sound.")


def interactive_fixed_mode():
    """Interactive mode with fixed generation."""
    
    print("🚀 Interactive Fixed Neural Translation")
    print("Commands: 'en2iu <text>' or 'iu2en <text>' or 'quit'")
    print("Example: en2iu Thank you, Mr. Speaker")
    print("-" * 50)
    
    # Load model
    model_path = "nunavut_mt/models/improved_gpu/improved_translation_model.pt"
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load tokenizer
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    from train_efficient_gpu import EfficientTranslator
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']
    
    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print("✅ Model loaded! Ready for translation.")
    
    while True:
        try:
            if not sys.stdin.isatty():
                print("Interactive mode requires a terminal.")
                break
                
            user_input = input("\n> ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if user_input.startswith('en2iu '):
                text = user_input[6:]
                with torch.no_grad():
                    result = translate_with_fixed_generation(
                        model, tokenizer, text, "en2iu", device, min_length=2
                    )
                print(f"EN → IU: {result}")
                
            elif user_input.startswith('iu2en '):
                text = user_input[6:]
                with torch.no_grad():
                    result = translate_with_fixed_generation(
                        model, tokenizer, text, "iu2en", device, min_length=2
                    )
                print(f"IU → EN: {result}")
                
            else:
                print("Please use format: 'en2iu <text>' or 'iu2en <text>'")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except EOFError:
            print("\nInput stream ended. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_fixed_mode()
    else:
        test_fixed_translator()
