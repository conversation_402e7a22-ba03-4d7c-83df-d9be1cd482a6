#!/usr/bin/env python3
"""
Enhanced translator using the web-enhanced dictionary.
"""

import json
import re
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedTranslator:
    """Enhanced English-Inuktitut translator with web-scraped vocabulary."""
    
    def __init__(self):
        """Load the enhanced dictionary."""
        
        # Try to load the merged dictionary first
        merged_dict_path = Path("nunavut_mt/models/web_dictionary/merged_dictionary.json")
        if merged_dict_path.exists():
            with open(merged_dict_path, 'r', encoding='utf-8') as f:
                self.word_dict = json.load(f)
            logger.info(f"Loaded enhanced dictionary with {len(self.word_dict)} entries")
        else:
            # Fallback to original dictionary
            dict_path = Path("nunavut_mt/models/dictionary/word_dictionary.json")
            if dict_path.exists():
                with open(dict_path, 'r', encoding='utf-8') as f:
                    self.word_dict = json.load(f)
                logger.info(f"Loaded original dictionary with {len(self.word_dict)} entries")
            else:
                logger.error("No dictionary found!")
                self.word_dict = {}
        
        # Create reverse dictionary for IU→EN
        self.reverse_dict = {}
        for en_word, iu_data in self.word_dict.items():
            iu_word = iu_data['translation']
            confidence = iu_data['confidence']
            
            if iu_word not in self.reverse_dict or confidence > self.reverse_dict[iu_word]['confidence']:
                self.reverse_dict[iu_word] = {'translation': en_word, 'confidence': confidence}
    
    def translate_en2iu(self, text):
        """Enhanced English to Inuktitut translation."""
        
        original_text = text
        text = text.lower().strip()
        
        # Handle common phrases first
        phrase_translations = {
            'thank you': 'ᖁᔭᓐᓇᒦᒃ',
            'mr. speaker': 'ᐅᖃᖅᑎ',
            'mr speaker': 'ᐅᖃᖅᑎ',
            'good morning': 'ᐅᓪᓗᒃ ᐱᐅᓂᖅ',
            'good evening': 'ᐅᓐᓄᒃ ᐱᐅᓂᖅ',
            'good night': 'ᐅᓐᓄᒃ ᐱᐅᓂᖅ',
            'how are you': 'ᖃᓄᐃᓕᖓᕙ',
            'i love you': 'ᓇᒡᓕᒋᔭᕋ',
            'see you later': 'ᑕᑯᓂᐊᕋᒃᑭᑦ',
            'excuse me': 'ᒪᒥᐊᓇᖅ',
            'i am sorry': 'ᒪᒥᐊᓇᖅ',
            'you are welcome': 'ᐃᓚᐅᑎᒋᔭᕋ'
        }
        
        # Check for exact phrase matches
        for phrase, translation in phrase_translations.items():
            if phrase in text:
                return translation
        
        # Word-by-word translation with improved logic
        words = re.findall(r'\b\w+\b', text)
        translated_words = []
        
        for word in words:
            translation = self.get_best_translation(word)
            translated_words.append(translation)
        
        if translated_words:
            result = " ".join(translated_words)
            # Clean up multiple spaces
            result = re.sub(r'\s+', ' ', result).strip()
            return result
        else:
            return f"[No translation found for: {original_text}]"
    
    def get_best_translation(self, word):
        """Get the best translation for a word."""
        
        word = word.lower()
        
        # Direct lookup
        if word in self.word_dict:
            data = self.word_dict[word]
            if data['confidence'] >= 10:  # Only use confident translations
                return data['translation']
        
        # Try variations
        variations = [
            word + 's',  # plural
            word + 'ed', # past tense
            word + 'ing', # present participle
            word[:-1] if word.endswith('s') else None,  # remove plural
            word[:-2] if word.endswith('ed') else None,  # remove past tense
            word[:-3] if word.endswith('ing') else None,  # remove -ing
        ]
        
        for variation in variations:
            if variation and variation in self.word_dict:
                data = self.word_dict[variation]
                if data['confidence'] >= 10:
                    return data['translation']
        
        # Partial matching for compound words
        for dict_word in self.word_dict:
            if (word in dict_word or dict_word in word) and len(dict_word) > 3:
                data = self.word_dict[dict_word]
                if data['confidence'] >= 20:  # Higher threshold for partial matches
                    return data['translation']
        
        # Return original word in brackets if no translation found
        return f"[{word}]"
    
    def translate_iu2en(self, text):
        """Enhanced Inuktitut to English translation."""
        
        text = text.strip()
        
        # Direct lookup
        if text in self.reverse_dict:
            return self.reverse_dict[text]['translation']
        
        # Word-by-word for multiple words
        words = text.split()
        translated_words = []
        
        for word in words:
            if word in self.reverse_dict:
                translated_words.append(self.reverse_dict[word]['translation'])
            else:
                translated_words.append(f"[{word}]")
        
        if translated_words:
            return " ".join(translated_words)
        else:
            return f"[No translation found for: {text}]"
    
    def get_dictionary_stats(self):
        """Get statistics about the dictionary."""
        
        total = len(self.word_dict)
        
        # Count by source
        corpus_count = sum(1 for v in self.word_dict.values() 
                          if v.get('source') not in ['tusaalanga', 'manual'])
        web_count = sum(1 for v in self.word_dict.values() 
                       if v.get('source') == 'tusaalanga')
        manual_count = sum(1 for v in self.word_dict.values() 
                          if v.get('source') == 'manual')
        
        # Count by confidence
        high_conf = sum(1 for v in self.word_dict.values() if v.get('confidence', 0) >= 50)
        med_conf = sum(1 for v in self.word_dict.values() if 10 <= v.get('confidence', 0) < 50)
        low_conf = sum(1 for v in self.word_dict.values() if v.get('confidence', 0) < 10)
        
        return {
            'total': total,
            'corpus': corpus_count,
            'web': web_count,
            'manual': manual_count,
            'high_confidence': high_conf,
            'medium_confidence': med_conf,
            'low_confidence': low_conf
        }


def test_enhanced_translator():
    """Test the enhanced translator."""
    
    print("🌐 Testing Enhanced Web Dictionary Translator")
    print("=" * 60)
    
    translator = EnhancedTranslator()
    
    # Show dictionary stats
    stats = translator.get_dictionary_stats()
    print(f"\n📊 Dictionary Statistics:")
    print(f"   Total entries: {stats['total']:,}")
    print(f"   - Corpus: {stats['corpus']:,}")
    print(f"   - Web scraped: {stats['web']:,}")
    print(f"   - Manual: {stats['manual']:,}")
    print(f"   High confidence (≥50): {stats['high_confidence']:,}")
    print(f"   Medium confidence (10-49): {stats['medium_confidence']:,}")
    print(f"   Low confidence (<10): {stats['low_confidence']:,}")
    
    # Test sentences - mix of parliamentary and everyday language
    test_sentences = [
        # Parliamentary (should work well)
        "Thank you, Mr. Speaker",
        "The government is working",
        "Minister of Health",
        
        # Everyday language (new additions)
        "Hello, how are you?",
        "Good morning",
        "My mother and father",
        "One, two, three",
        "The dog is big",
        "I love fish",
        "The water is cold",
        "Red and blue",
        
        # Mixed
        "Thank you for the food",
        "Good evening, Minister",
        "The bear is white",
        "Five members voted"
    ]
    
    print(f"\n🚀 Enhanced Translation Results:")
    print("=" * 70)
    
    success_count = 0
    partial_count = 0
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n{i:2d}. EN: {sentence}")
        
        # Translate EN → IU
        result = translator.translate_en2iu(sentence)
        print(f"    IU: {result}")
        
        # Analyze result quality
        bracket_count = result.count('[')
        total_words = len(sentence.split())
        
        if bracket_count == 0:
            success_count += 1
            print(f"    ✅ Complete translation")
        elif bracket_count <= total_words // 2:
            partial_count += 1
            print(f"    🔶 Partial translation ({bracket_count} unknown words)")
        else:
            print(f"    ⚠️  Mostly unknown ({bracket_count} unknown words)")
        
        # Try reverse translation for complete translations
        if bracket_count == 0:
            reverse = translator.translate_iu2en(result)
            print(f"    ←: {reverse}")
    
    print(f"\n📊 Results Summary:")
    print(f"   - Complete translations: {success_count}/{len(test_sentences)}")
    print(f"   - Partial translations: {partial_count}/{len(test_sentences)}")
    print(f"   - Success rate: {(success_count + partial_count)/len(test_sentences)*100:.1f}%")
    
    return translator


def interactive_enhanced_mode():
    """Interactive mode with enhanced translator."""
    
    print("\n🌐 Interactive Enhanced Translation")
    print("Commands: 'en2iu <text>' or 'iu2en <text>' or 'stats' or 'quit'")
    print("Example: en2iu Hello, my name is John")
    print("-" * 60)
    
    translator = EnhancedTranslator()
    
    while True:
        try:
            user_input = input("\n> ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if user_input.lower() == 'stats':
                stats = translator.get_dictionary_stats()
                print(f"\n📊 Dictionary Statistics:")
                for key, value in stats.items():
                    print(f"   {key.replace('_', ' ').title()}: {value:,}")
                continue
            
            if user_input.startswith('en2iu '):
                text = user_input[6:]
                result = translator.translate_en2iu(text)
                print(f"EN → IU: {result}")
                
            elif user_input.startswith('iu2en '):
                text = user_input[6:]
                result = translator.translate_iu2en(text)
                print(f"IU → EN: {result}")
                
            else:
                print("Commands: 'en2iu <text>', 'iu2en <text>', 'stats', or 'quit'")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except EOFError:
            print("\nInput stream ended. Goodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    print("🌐 Enhanced Nunavut MT with Web Dictionary")
    print("=" * 50)
    
    # Test the enhanced translator
    translator = test_enhanced_translator()
    
    # Offer interactive mode
    print(f"\n💡 Want to try interactive mode? (y/n)")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            interactive_enhanced_mode()
    except (KeyboardInterrupt, EOFError):
        print("\nGoodbye!")
