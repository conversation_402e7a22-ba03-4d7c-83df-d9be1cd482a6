#!/usr/bin/env python3
"""
Efficient GPU training for Nunavut MT - optimized for RTX 4070.

This script trains a smaller, more efficient neural translation model
that fits within 8GB GPU memory.
"""

import os
import sys
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import sentencepiece as spm
from pathlib import Path
import json
import time
from tqdm import tqdm
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EfficientTranslator(nn.Module):
    """Efficient neural translator optimized for GPU memory."""
    
    def __init__(self, vocab_size, d_model=256, nhead=4, num_layers=3, max_seq_len=128):
        super().__init__()
        self.d_model = d_model
        self.vocab_size = vocab_size
        self.max_seq_len = max_seq_len
        
        # Embeddings
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.pos_encoding = nn.Parameter(torch.randn(max_seq_len, d_model))
        
        # Encoder-decoder transformer
        self.transformer = nn.Transformer(
            d_model=d_model,
            nhead=nhead,
            num_encoder_layers=num_layers,
            num_decoder_layers=num_layers,
            dim_feedforward=d_model * 2,  # Smaller feedforward
            dropout=0.1,
            batch_first=True
        )
        
        # Output projection
        self.output_proj = nn.Linear(d_model, vocab_size)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, src, tgt):
        # Add positional encoding
        src_seq_len = min(src.size(1), self.max_seq_len)
        tgt_seq_len = min(tgt.size(1), self.max_seq_len)
        
        # Embed source
        src_emb = self.embedding(src[:, :src_seq_len]) * (self.d_model ** 0.5)
        src_emb = src_emb + self.pos_encoding[:src_seq_len].unsqueeze(0)
        src_emb = self.dropout(src_emb)
        
        # Embed target
        tgt_emb = self.embedding(tgt[:, :tgt_seq_len]) * (self.d_model ** 0.5)
        tgt_emb = tgt_emb + self.pos_encoding[:tgt_seq_len].unsqueeze(0)
        tgt_emb = self.dropout(tgt_emb)
        
        # Create target mask (causal)
        tgt_mask = nn.Transformer.generate_square_subsequent_mask(tgt_seq_len).to(tgt.device)
        
        # Transform
        output = self.transformer(src_emb, tgt_emb, tgt_mask=tgt_mask)
        
        # Project to vocabulary
        return self.output_proj(output)


class SimpleDataset(Dataset):
    """Simple dataset for efficient training."""
    
    def __init__(self, en_file, iu_file, tokenizer, max_len=64, max_samples=5000):
        self.tokenizer = tokenizer
        self.max_len = max_len
        
        # Load data
        with open(en_file, 'r', encoding='utf-8') as f:
            en_lines = [line.strip() for line in f if line.strip()]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_lines = [line.strip() for line in f if line.strip()]
        
        # Take smaller subset for memory efficiency
        self.en_lines = en_lines[:max_samples]
        self.iu_lines = iu_lines[:max_samples]
        
        logger.info(f"Loaded {len(self.en_lines)} training pairs")
    
    def __len__(self):
        return len(self.en_lines)
    
    def __getitem__(self, idx):
        en_text = self.en_lines[idx]
        iu_text = self.iu_lines[idx]
        
        # Tokenize with direction token
        en_tokens = self.tokenizer.encode_as_ids(f"<en2iu> {en_text}")
        iu_tokens = self.tokenizer.encode_as_ids(iu_text)
        
        # Truncate
        if len(en_tokens) > self.max_len:
            en_tokens = en_tokens[:self.max_len]
        if len(iu_tokens) > self.max_len:
            iu_tokens = iu_tokens[:self.max_len]
        
        return {
            'src': torch.tensor(en_tokens, dtype=torch.long),
            'tgt': torch.tensor(iu_tokens, dtype=torch.long)
        }


def collate_fn(batch):
    """Simple collate function."""
    src_seqs = [item['src'] for item in batch]
    tgt_seqs = [item['tgt'] for item in batch]
    
    # Pad to max length in batch
    max_src_len = max(len(seq) for seq in src_seqs)
    max_tgt_len = max(len(seq) for seq in tgt_seqs)
    
    # Pad sequences
    src_padded = []
    tgt_padded = []
    labels = []
    
    for src, tgt in zip(src_seqs, tgt_seqs):
        # Pad source
        src_pad = torch.cat([src, torch.zeros(max_src_len - len(src), dtype=torch.long)])
        src_padded.append(src_pad)
        
        # Pad target (input)
        tgt_pad = torch.cat([tgt, torch.zeros(max_tgt_len - len(tgt), dtype=torch.long)])
        tgt_padded.append(tgt_pad)
        
        # Create labels (target shifted)
        label_pad = torch.cat([tgt[1:], torch.tensor([3]), torch.full((max_tgt_len - len(tgt),), -100, dtype=torch.long)])
        labels.append(label_pad)
    
    return {
        'src': torch.stack(src_padded),
        'tgt': torch.stack(tgt_padded),
        'labels': torch.stack(labels)
    }


def train_efficient_model():
    """Train an efficient model for RTX 4070."""
    
    # Check GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        # Clear GPU cache
        torch.cuda.empty_cache()
    
    # Paths
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    data_dir = Path("nunavut_mt/data/processed")
    model_dir = Path("nunavut_mt/models/efficient_gpu")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    vocab_size = tokenizer.get_piece_size()
    
    # Create dataset
    logger.info("Creating efficient dataset...")
    dataset = SimpleDataset(
        en_file=data_dir / "train.en",
        iu_file=data_dir / "train.iu",
        tokenizer=tokenizer,
        max_len=64,  # Shorter sequences
        max_samples=5000  # Smaller dataset
    )
    
    # Create dataloader
    dataloader = DataLoader(
        dataset, 
        batch_size=16,  # Larger batch size with smaller model
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=0
    )
    
    # Create efficient model
    logger.info("Creating efficient model...")
    model = EfficientTranslator(
        vocab_size=vocab_size, 
        d_model=256,  # Smaller model
        nhead=4, 
        num_layers=3,
        max_seq_len=64
    )
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model parameters: {total_params:,}")
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, weight_decay=0.01)
    
    # Training loop
    logger.info("Starting efficient GPU training...")
    model.train()
    
    num_epochs = 5  # More epochs with smaller model
    total_loss = 0
    step = 0
    
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch + 1}/{num_epochs}")
        
        epoch_loss = 0
        progress_bar = tqdm(dataloader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            # Move to GPU
            src = batch['src'].to(device)
            tgt = batch['tgt'].to(device)
            labels = batch['labels'].to(device)
            
            # Forward pass
            outputs = model(src, tgt)
            
            # Calculate loss
            loss = criterion(outputs.view(-1, vocab_size), labels.view(-1))
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            epoch_loss += loss.item()
            step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{epoch_loss / (progress_bar.n + 1):.4f}",
                'gpu_mem': f"{torch.cuda.memory_allocated() / 1e9:.1f}GB" if torch.cuda.is_available() else "N/A"
            })
            
            # Log periodically
            if step % 25 == 0:
                mem_info = f", GPU memory = {torch.cuda.memory_allocated() / 1e9:.1f}GB" if torch.cuda.is_available() else ""
                logger.info(f"Step {step}: loss = {loss.item():.4f}{mem_info}")
        
        logger.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(dataloader):.4f}")
    
    # Save model
    logger.info("Saving efficient model...")
    model_path = model_dir / "efficient_translation_model.pt"
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'model_config': {
            'd_model': 256,
            'nhead': 4,
            'num_layers': 3,
            'max_seq_len': 64
        },
        'tokenizer_path': tokenizer_path
    }, model_path)
    
    # Save training info
    training_info = {
        'vocab_size': vocab_size,
        'training_samples': len(dataset),
        'epochs': num_epochs,
        'final_loss': total_loss / step if step > 0 else 0,
        'model_path': str(model_path),
        'device': str(device),
        'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None,
        'model_params': total_params
    }
    
    with open(model_dir / "training_info.json", 'w') as f:
        json.dump(training_info, f, indent=2)
    
    logger.info(f"Training completed! Model saved to {model_path}")
    return model_path


def test_efficient_model(model_path):
    """Test the efficient trained model."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load model info
    checkpoint = torch.load(model_path, map_location=device)
    vocab_size = checkpoint['vocab_size']
    tokenizer_path = checkpoint['tokenizer_path']
    config = checkpoint['model_config']
    
    # Load tokenizer
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    model = EfficientTranslator(
        vocab_size=vocab_size, 
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # Test sentences
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "Hello, how are you?",
        "Good morning.",
        "The government is working."
    ]
    
    logger.info("Testing efficient trained model:")
    print("\n" + "="*60)
    print("🚀 EFFICIENT GPU-TRAINED MODEL TRANSLATIONS")
    print("="*60)
    
    with torch.no_grad():
        for sentence in test_sentences:
            # Tokenize input
            input_text = f"<en2iu> {sentence}"
            input_tokens = tokenizer.encode_as_ids(input_text)
            
            # Limit length
            if len(input_tokens) > 32:
                input_tokens = input_tokens[:32]
            
            src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
            
            # Simple greedy decoding
            max_len = 32
            output_tokens = [2]  # Start with BOS
            
            for _ in range(max_len):
                tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
                
                outputs = model(src_tensor, tgt_tensor)
                next_token = outputs[0, -1].argmax().item()
                
                if next_token == 3:  # EOS token
                    break
                    
                output_tokens.append(next_token)
            
            # Decode output
            if len(output_tokens) > 1:
                predicted_text = tokenizer.decode_ids(output_tokens[1:])  # Skip BOS
            else:
                predicted_text = "[No output generated]"
            
            print(f"EN: {sentence}")
            print(f"IU: {predicted_text}")
            print("-" * 40)


if __name__ == "__main__":
    logger.info("🚀 Starting efficient GPU training for Nunavut MT")
    
    try:
        # Train the efficient model
        model_path = train_efficient_model()
        
        # Test the trained model
        test_efficient_model(model_path)
        
        logger.info("🎉 Efficient GPU training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise
