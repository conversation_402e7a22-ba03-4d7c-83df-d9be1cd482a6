#!/usr/bin/env python3
"""
Stage 2 Advanced Training Script
Implements curriculum learning, mixed precision, and advanced optimization for Stage 2 model.
"""

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
try:
    from torch.amp import GradScaler, autocast
except ImportError:
    from torch.cuda.amp import GradScaler, autocast
import sentencepiece as spm
import json
import time
from pathlib import Path
from tqdm import tqdm
import logging
from typing import List, Tuple, Dict, Optional
import math

from stage2_model import Stage2Transformer, create_stage2_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Stage2Dataset(Dataset):
    """Enhanced dataset for Stage 2 training with curriculum learning."""
    
    def __init__(self, en_file: str, iu_file: str, tokenizer_path: str, max_length: int = 128):
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.load(tokenizer_path)
        self.max_length = max_length
        
        # Load data
        with open(en_file, 'r', encoding='utf-8') as f:
            self.en_sentences = [line.strip() for line in f if line.strip()]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            self.iu_sentences = [line.strip() for line in f if line.strip()]
        
        assert len(self.en_sentences) == len(self.iu_sentences)
        logger.info(f"Loaded {len(self.en_sentences)} sentence pairs")
        
        # Special tokens
        self.bos_id = self.tokenizer.bos_id()
        self.eos_id = self.tokenizer.eos_id()
        self.pad_id = self.tokenizer.pad_id()
    
    def __len__(self):
        return len(self.en_sentences)
    
    def __getitem__(self, idx):
        en_text = self.en_sentences[idx]
        iu_text = self.iu_sentences[idx]
        
        # Tokenize
        en_tokens = self.tokenizer.encode(en_text)
        iu_tokens = self.tokenizer.encode(iu_text)
        
        # Add special tokens and truncate
        en_tokens = [self.bos_id] + en_tokens[:self.max_length-2] + [self.eos_id]
        iu_tokens = [self.bos_id] + iu_tokens[:self.max_length-2] + [self.eos_id]
        
        # Pad to max length
        en_tokens += [self.pad_id] * (self.max_length - len(en_tokens))
        iu_tokens += [self.pad_id] * (self.max_length - len(iu_tokens))
        
        return {
            'src': torch.tensor(en_tokens[:self.max_length], dtype=torch.long),
            'tgt': torch.tensor(iu_tokens[:self.max_length], dtype=torch.long),
            'src_length': min(len(self.en_sentences[idx].split()) + 2, self.max_length),
            'tgt_length': min(len(self.iu_sentences[idx].split()) + 2, self.max_length)
        }

class LabelSmoothingLoss(nn.Module):
    """Label smoothing loss for better generalization."""
    
    def __init__(self, vocab_size: int, smoothing: float = 0.1, ignore_index: int = -100):
        super().__init__()
        self.vocab_size = vocab_size
        self.smoothing = smoothing
        self.ignore_index = ignore_index
        self.confidence = 1.0 - smoothing
    
    def forward(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        # Reshape inputs
        logits = logits.reshape(-1, logits.size(-1))  # [batch_size * seq_len, vocab_size]
        targets = targets.reshape(-1)  # [batch_size * seq_len]

        log_probs = torch.log_softmax(logits, dim=-1)

        # Create smoothed targets
        smooth_targets = torch.zeros_like(log_probs)
        smooth_targets.fill_(self.smoothing / (self.vocab_size - 1))
        smooth_targets.scatter_(1, targets.unsqueeze(1), self.confidence)

        # Mask padding tokens
        mask = (targets != self.ignore_index)
        smooth_targets = smooth_targets * mask.unsqueeze(1)

        loss = -torch.sum(smooth_targets * log_probs, dim=-1)
        loss = loss * mask

        return loss.sum() / mask.sum() if mask.sum() > 0 else torch.tensor(0.0, device=logits.device)

class CurriculumScheduler:
    """Curriculum learning scheduler."""
    
    def __init__(self, easy_epochs: int = 5, medium_epochs: int = 8, hard_epochs: int = 7):
        self.easy_epochs = easy_epochs
        self.medium_epochs = medium_epochs
        self.hard_epochs = hard_epochs
        self.total_epochs = easy_epochs + medium_epochs + hard_epochs
    
    def get_current_stage(self, epoch: int) -> str:
        if epoch < self.easy_epochs:
            return 'easy'
        elif epoch < self.easy_epochs + self.medium_epochs:
            return 'medium'
        else:
            return 'hard'

class DropoutScheduler:
    """Dynamic dropout scheduling."""
    
    def __init__(self, initial_dropout: float = 0.3, final_dropout: float = 0.1, total_epochs: int = 20):
        self.initial_dropout = initial_dropout
        self.final_dropout = final_dropout
        self.total_epochs = total_epochs
    
    def get_dropout(self, epoch: int) -> float:
        progress = epoch / self.total_epochs
        return self.initial_dropout - (self.initial_dropout - self.final_dropout) * progress

class Stage2Trainer:
    """Advanced trainer for Stage 2 model."""
    
    def __init__(self, model: Stage2Transformer, tokenizer_path: str, device: torch.device):
        self.model = model
        self.device = device
        self.tokenizer_path = tokenizer_path
        
        # Load tokenizer
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.load(tokenizer_path)
        
        # Training components
        if device.type == 'cuda':
            try:
                self.scaler = GradScaler('cuda')  # For mixed precision
            except TypeError:
                self.scaler = GradScaler()  # Fallback for older PyTorch
        else:
            self.scaler = None  # No mixed precision on CPU
        self.curriculum_scheduler = CurriculumScheduler()
        self.dropout_scheduler = DropoutScheduler()
        
        # Loss function with label smoothing
        self.criterion = LabelSmoothingLoss(
            vocab_size=self.tokenizer.vocab_size(),
            smoothing=0.1,
            ignore_index=self.tokenizer.pad_id()
        )
        
        # Optimizer with weight decay
        self.optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=8e-5,  # Stage 2 learning rate
            betas=(0.9, 0.98),
            eps=1e-9,
            weight_decay=1e-4
        )
        
        # Learning rate scheduler
        self.lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, T_0=5, T_mult=2, eta_min=1e-6
        )
    
    def create_masks(self, src: torch.Tensor, tgt: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Create attention masks."""
        # Source mask (padding)
        src_mask = (src != self.tokenizer.pad_id()).unsqueeze(1).unsqueeze(2)
        
        # Target mask (padding + causal)
        tgt_len = tgt.size(1)
        tgt_mask = (tgt != self.tokenizer.pad_id()).unsqueeze(1).unsqueeze(2)
        causal_mask = self.model.generate_square_subsequent_mask(tgt_len).to(self.device)
        tgt_mask = tgt_mask & causal_mask.unsqueeze(0)
        
        return src_mask, tgt_mask
    
    def train_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train one epoch with mixed precision."""
        self.model.train()
        total_loss = 0
        num_batches = len(dataloader)
        
        # Update dropout based on schedule
        current_dropout = self.dropout_scheduler.get_dropout(epoch)
        for module in self.model.modules():
            if isinstance(module, nn.Dropout):
                module.p = current_dropout
        
        progress_bar = tqdm(dataloader, desc=f"Epoch {epoch+1}")
        
        for batch_idx, batch in enumerate(progress_bar):
            src = batch['src'].to(self.device)
            tgt = batch['tgt'].to(self.device)
            
            # Prepare input and target
            tgt_input = tgt[:, :-1]
            tgt_output = tgt[:, 1:]
            
            # Create masks
            src_mask, tgt_mask = self.create_masks(src, tgt_input)
            
            self.optimizer.zero_grad()

            # Forward pass with optional mixed precision
            if self.scaler is not None:
                # GPU with mixed precision
                with autocast('cuda'):
                    logits = self.model(src, tgt_input, src_mask, tgt_mask)
                    loss = self.criterion(logits, tgt_output)

                # Mixed precision backward pass
                self.scaler.scale(loss).backward()

                # Gradient clipping
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                # Optimizer step
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                # CPU or no mixed precision
                logits = self.model(src, tgt_input, src_mask, tgt_mask)
                loss = self.criterion(logits, tgt_output)

                # Regular backward pass
                loss.backward()

                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

                # Optimizer step
                self.optimizer.step()
            
            total_loss += loss.item()
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'avg_loss': f'{total_loss/(batch_idx+1):.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.2e}',
                'dropout': f'{current_dropout:.3f}'
            })
        
        # Update learning rate
        self.lr_scheduler.step()
        
        avg_loss = total_loss / num_batches
        return {
            'train_loss': avg_loss,
            'learning_rate': self.optimizer.param_groups[0]['lr'],
            'dropout': current_dropout
        }
    
    def save_checkpoint(self, epoch: int, metrics: Dict[str, float], save_dir: str):
        """Save model checkpoint."""
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'lr_scheduler_state_dict': self.lr_scheduler.state_dict(),
            'scaler_state_dict': self.scaler.state_dict() if self.scaler is not None else None,
            'metrics': metrics,
            'model_config': {
                'vocab_size': self.tokenizer.vocab_size(),
                'd_model': 320,
                'num_heads': 8,
                'num_layers': 4,
                'd_ff': 1280,
                'max_seq_len': 128
            }
        }
        
        torch.save(checkpoint, save_path / f"stage2_checkpoint_epoch_{epoch}.pt")
        torch.save(checkpoint, save_path / "stage2_latest.pt")
        
        logger.info(f"Checkpoint saved: epoch {epoch}")

def main():
    """Main training function."""
    # Setup
    if torch.cuda.is_available():
        device = torch.device("cuda")
        logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device("cpu")
        logger.info("CUDA not available, using CPU")

    logger.info(f"Device: {device}")
    
    # Paths
    tokenizer_path = "models/tokenizer/tokenizer_v16000.model"
    data_dir = Path("data/stage2")
    save_dir = "models/stage2"
    
    # Create model
    model = create_stage2_model(vocab_size=16000, device=device)
    trainer = Stage2Trainer(model, tokenizer_path, device)
    
    # Curriculum learning
    curriculum = trainer.curriculum_scheduler
    
    logger.info("Starting Stage 2 training with curriculum learning...")
    logger.info(f"Total epochs: {curriculum.total_epochs}")
    logger.info(f"Easy: {curriculum.easy_epochs}, Medium: {curriculum.medium_epochs}, Hard: {curriculum.hard_epochs}")
    
    for epoch in range(curriculum.total_epochs):
        # Get current curriculum stage
        stage = curriculum.get_current_stage(epoch)
        
        # Load appropriate dataset
        en_file = data_dir / f"stage2_{stage}.en"
        iu_file = data_dir / f"stage2_{stage}.iu"
        
        dataset = Stage2Dataset(str(en_file), str(iu_file), tokenizer_path)
        dataloader = DataLoader(dataset, batch_size=24, shuffle=True, num_workers=2)
        
        logger.info(f"Epoch {epoch+1}/{curriculum.total_epochs} - Stage: {stage.upper()}")
        
        # Train epoch
        metrics = trainer.train_epoch(dataloader, epoch)
        
        logger.info(f"Epoch {epoch+1} completed - Loss: {metrics['train_loss']:.4f}, "
                   f"LR: {metrics['learning_rate']:.2e}")
        
        # Save checkpoint every 5 epochs
        if (epoch + 1) % 5 == 0:
            trainer.save_checkpoint(epoch, metrics, save_dir)
    
    # Save final model
    trainer.save_checkpoint(curriculum.total_epochs - 1, metrics, save_dir)
    logger.info("Stage 2 training completed!")

if __name__ == "__main__":
    main()
