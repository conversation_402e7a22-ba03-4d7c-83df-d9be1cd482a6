#!/usr/bin/env python3
"""
Enhanced Dataset Preparation for Stage 2 Training
Implements data cleaning, augmentation, and balanced sampling for 25K sentence pairs.
"""

import os
import sys
import json
import random
from pathlib import Path
from typing import List, Tuple, Dict
import logging
from collections import defaultdict
import re

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Stage2DatasetPreparer:
    """Enhanced dataset preparation for Stage 2 training."""
    
    def __init__(self, data_dir: str = "../split",
                 output_dir: str = "data/stage2"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Stage 2 parameters from comprehensive plan
        self.target_size = 25000
        self.min_length = 5
        self.max_length = 50  # Increased from Stage 1
        self.quality_threshold = 0.7
        
    def load_parallel_data(self) -> List[Tuple[str, str]]:
        """Load and combine all available parallel data."""
        logger.info("Loading parallel data...")
        
        pairs = []
        
        # Load training data
        en_file = self.data_dir / "train.en"
        iu_file = self.data_dir / "train.iu"
        
        if en_file.exists() and iu_file.exists():
            with open(en_file, 'r', encoding='utf-8') as f_en, \
                 open(iu_file, 'r', encoding='utf-8') as f_iu:
                
                en_lines = f_en.readlines()
                iu_lines = f_iu.readlines()
                
                for en, iu in zip(en_lines, iu_lines):
                    en = en.strip()
                    iu = iu.strip()
                    if en and iu:
                        pairs.append((en, iu))
        
        # Load dev data for additional training
        dev_en = self.data_dir / "dev-dedup.en"
        dev_iu = self.data_dir / "dev-dedup.iu"
        
        if dev_en.exists() and dev_iu.exists():
            with open(dev_en, 'r', encoding='utf-8') as f_en, \
                 open(dev_iu, 'r', encoding='utf-8') as f_iu:
                
                en_lines = f_en.readlines()
                iu_lines = f_iu.readlines()
                
                for en, iu in zip(en_lines, iu_lines):
                    en = en.strip()
                    iu = iu.strip()
                    if en and iu:
                        pairs.append((en, iu))
        
        logger.info(f"Loaded {len(pairs)} parallel sentence pairs")
        return pairs
    
    def clean_sentence_pair(self, en: str, iu: str) -> Tuple[str, str, float]:
        """Clean and score a sentence pair."""
        # Basic cleaning
        en = re.sub(r'\s+', ' ', en.strip())
        iu = re.sub(r'\s+', ' ', iu.strip())
        
        # Remove pairs with problematic characters
        if any(char in en + iu for char in ['<', '>', '[', ']', '{', '}']):
            return en, iu, 0.0
        
        # Length filtering
        en_words = len(en.split())
        iu_words = len(iu.split())
        
        if en_words < self.min_length or iu_words < self.min_length:
            return en, iu, 0.0
        
        if en_words > self.max_length or iu_words > self.max_length:
            return en, iu, 0.0
        
        # Quality scoring (simple heuristics)
        quality_score = 1.0
        
        # Length ratio check
        length_ratio = min(en_words, iu_words) / max(en_words, iu_words)
        if length_ratio < 0.3:  # Very different lengths
            quality_score *= 0.5
        
        # Check for repeated patterns
        if len(set(en.split())) < len(en.split()) * 0.5:  # Too repetitive
            quality_score *= 0.7
        
        # Check for numbers alignment (simple heuristic)
        en_numbers = re.findall(r'\d+', en)
        iu_numbers = re.findall(r'\d+', iu)
        if len(en_numbers) != len(iu_numbers):
            quality_score *= 0.8
        
        return en, iu, quality_score
    
    def augment_data(self, pairs: List[Tuple[str, str]]) -> List[Tuple[str, str]]:
        """Apply data augmentation techniques."""
        logger.info("Applying data augmentation...")
        
        augmented_pairs = []
        
        for en, iu in pairs:
            # Original pair
            augmented_pairs.append((en, iu))
            
            # Simple augmentation: add punctuation variations
            if not en.endswith('.') and not en.endswith('?') and not en.endswith('!'):
                augmented_pairs.append((en + '.', iu + '.'))
            
            # Case variations for English (if appropriate)
            if en.islower() or en.isupper():
                en_title = en.capitalize()
                augmented_pairs.append((en_title, iu))
        
        logger.info(f"Augmented to {len(augmented_pairs)} pairs")
        return augmented_pairs
    
    def create_curriculum_splits(self, pairs: List[Tuple[str, str]]) -> Dict[str, List[Tuple[str, str]]]:
        """Create curriculum learning splits by sentence length."""
        logger.info("Creating curriculum learning splits...")
        
        # Sort by average sentence length
        def avg_length(pair):
            en, iu = pair
            return (len(en.split()) + len(iu.split())) / 2
        
        sorted_pairs = sorted(pairs, key=avg_length)
        
        # Create splits
        total = len(sorted_pairs)
        splits = {
            'easy': sorted_pairs[:total//3],           # Shortest sentences
            'medium': sorted_pairs[total//3:2*total//3], # Medium sentences
            'hard': sorted_pairs[2*total//3:]           # Longest sentences
        }
        
        logger.info(f"Curriculum splits - Easy: {len(splits['easy'])}, "
                   f"Medium: {len(splits['medium'])}, Hard: {len(splits['hard'])}")
        
        return splits
    
    def prepare_stage2_dataset(self):
        """Main function to prepare Stage 2 dataset."""
        logger.info("Starting Stage 2 dataset preparation...")
        
        # Load data
        raw_pairs = self.load_parallel_data()
        
        # Clean and filter
        logger.info("Cleaning and filtering data...")
        cleaned_pairs = []
        quality_scores = []
        
        for en, iu in raw_pairs:
            clean_en, clean_iu, score = self.clean_sentence_pair(en, iu)
            if score >= self.quality_threshold:
                cleaned_pairs.append((clean_en, clean_iu))
                quality_scores.append(score)
        
        logger.info(f"After cleaning: {len(cleaned_pairs)} high-quality pairs")
        
        # Remove duplicates
        unique_pairs = list(set(cleaned_pairs))
        logger.info(f"After deduplication: {len(unique_pairs)} unique pairs")
        
        # Augment if needed to reach target size
        if len(unique_pairs) < self.target_size:
            augmented_pairs = self.augment_data(unique_pairs)
            # Take up to target size
            final_pairs = augmented_pairs[:self.target_size]
        else:
            # Sample best quality pairs
            paired_with_scores = list(zip(unique_pairs, quality_scores[:len(unique_pairs)]))
            paired_with_scores.sort(key=lambda x: x[1], reverse=True)
            final_pairs = [pair for pair, score in paired_with_scores[:self.target_size]]
        
        logger.info(f"Final dataset size: {len(final_pairs)} pairs")
        
        # Create curriculum splits
        curriculum_splits = self.create_curriculum_splits(final_pairs)
        
        # Save datasets
        self.save_dataset(final_pairs, "stage2_full")
        
        for split_name, split_pairs in curriculum_splits.items():
            self.save_dataset(split_pairs, f"stage2_{split_name}")
        
        # Save statistics
        stats = {
            'total_pairs': len(final_pairs),
            'curriculum_splits': {k: len(v) for k, v in curriculum_splits.items()},
            'avg_en_length': sum(len(pair[0].split()) for pair in final_pairs) / len(final_pairs) if final_pairs else 0,
            'avg_iu_length': sum(len(pair[1].split()) for pair in final_pairs) / len(final_pairs) if final_pairs else 0,
            'quality_threshold': self.quality_threshold,
            'target_size': self.target_size
        }
        
        with open(self.output_dir / "stage2_stats.json", 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        
        logger.info("Stage 2 dataset preparation completed!")
        logger.info(f"Statistics saved to: {self.output_dir / 'stage2_stats.json'}")
        
        return final_pairs, curriculum_splits
    
    def save_dataset(self, pairs: List[Tuple[str, str]], name: str):
        """Save dataset to files."""
        en_file = self.output_dir / f"{name}.en"
        iu_file = self.output_dir / f"{name}.iu"
        
        with open(en_file, 'w', encoding='utf-8') as f_en, \
             open(iu_file, 'w', encoding='utf-8') as f_iu:
            
            for en, iu in pairs:
                f_en.write(en + '\n')
                f_iu.write(iu + '\n')
        
        logger.info(f"Saved {len(pairs)} pairs to {name}.{{en,iu}}")

def main():
    """Main function."""
    preparer = Stage2DatasetPreparer()
    pairs, splits = preparer.prepare_stage2_dataset()
    
    print(f"\n✅ Stage 2 dataset preparation completed!")
    print(f"📊 Total pairs: {len(pairs)}")
    print(f"📚 Curriculum splits: {[f'{k}={len(v)}' for k, v in splits.items()]}")
    print(f"💾 Files saved to: nunavut_mt/data/stage2/")

if __name__ == "__main__":
    main()
