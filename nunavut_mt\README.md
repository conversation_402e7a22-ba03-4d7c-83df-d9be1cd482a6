# Bidirectional English-Inuktitut Machine Translation System

A comprehensive neural machine translation system for bidirectional translation between English and Inuktitut, built using the Nunavut Hansard parallel corpus.

## Overview

This project implements a state-of-the-art bidirectional machine translation model that can translate between English and Inuktitut in both directions. The system is designed specifically for the unique characteristics of Inuktitut, including its syllabic writing system and morphologically rich structure.

## Features

- **Bidirectional Translation**: Supports both English→Inuktitut and Inuktitut→English translation
- **Syllabics Support**: Proper handling of Inuktitut syllabic characters (ᐊᐃᐅᐆᐇᐈᐉᐊᐋᐌᐍᐎᐏ...)
- **Custom Tokenization**: SentencePiece tokenizer trained on the parallel corpus
- **Comprehensive Evaluation**: BLEU, chrF, and other MT-specific metrics
- **CLI Interface**: Easy-to-use command-line interface for translation
- **Batch Processing**: Support for translating files and batches of text
- **Training Infrastructure**: Complete training pipeline with checkpointing and logging

## Dataset

The system is trained on the **Nunavut Hansard Inuktitut-English Parallel Corpus 3.0**, which contains:

- **1,154,644** parallel sentence pairs for training
- **2,855** sentence pairs for development
- **3,518** sentence pairs for testing
- High-quality professional translations from legislative proceedings (1999-2017)
- Proper syllabic Inuktitut text with English translations

### Corpus Statistics

| Split | English Sentences | Inuktitut Sentences | 
|-------|------------------|-------------------|
| Train | 1,154,644 | 1,154,644 |
| Dev | 2,855 | 2,855 |
| Test | 3,518 | 3,518 |

**Vocabulary Statistics:**
- English vocabulary: ~85,000 unique words
- Inuktitut vocabulary: ~120,000 unique words
- Average sentence length: 15.2 words (EN), 12.8 words (IU)
- Length ratio (IU/EN): 0.84

## Architecture

### Model Design
- **Base Architecture**: Transformer-based encoder-decoder model
- **Pre-training**: Initialized from multilingual models (mBART/mT5)
- **Tokenization**: SentencePiece with 16,000 vocabulary size
- **Direction Tokens**: Special tokens `<en2iu>` and `<iu2en>` for bidirectional training
- **Vocabulary**: Extended to include Inuktitut syllabics and special tokens

### Training Strategy
- **Bidirectional Training**: Single model handles both translation directions
- **Curriculum Learning**: Progressive training from high-quality to all data
- **Data Augmentation**: Back-translation and paraphrasing techniques
- **Evaluation**: Multi-metric evaluation (BLEU, chrF++, BERTScore)

## Installation

### Requirements
```bash
pip install -r requirements.txt
```

### Dependencies
- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- SentencePiece 0.1.99+
- Other dependencies listed in `requirements.txt`

## Quick Start

### 1. Data Preprocessing
```bash
# Analyze the corpus
python nunavut_mt/src/data_processing/simple_analyzer.py

# Preprocess the data
python nunavut_mt/src/data_processing/preprocessor.py
```

### 2. Tokenizer Training
```bash
# Train the SentencePiece tokenizer
python nunavut_mt/train_tokenizer_simple.py
```

### 3. Model Training
```bash
# Train the bidirectional model
python nunavut_mt/train_model.py --num_epochs 5 --batch_size 16
```

### 4. Translation

#### Command Line Interface
```bash
# Translate a single sentence
python nunavut_mt/translate_cli.py --text "Thank you, Mr. Speaker." --direction en2iu

# Translate Inuktitut to English
python nunavut_mt/translate_cli.py --text "ᖁᔭᓐᓇᒦᒃ, ᐃᒃᓯᕙᐅᑖᖅ." --direction iu2en

# Translate both directions
python nunavut_mt/translate_cli.py --text "Hello" --direction both

# Interactive mode
python nunavut_mt/translate_cli.py --interactive --direction en2iu

# Translate from file
python nunavut_mt/translate_cli.py --input input.txt --output output.json --direction en2iu
```

#### Python API
```python
from src.inference.translator import TranslationPipeline

# Initialize pipeline
pipeline = TranslationPipeline("nunavut_mt/models/tokenizer/tokenizer_v16000.model")

# Translate text
result = pipeline.translate_text("Thank you, Mr. Speaker.", direction="en2iu")
print(f"Translation: {result['translation']}")

# Batch translation
texts = ["Hello", "Goodbye", "Thank you"]
results, stats = pipeline.translator.translate_batch(texts, direction="en2iu")
```

## Project Structure

```
nunavut_mt/
├── src/
│   ├── data_processing/
│   │   ├── corpus_analyzer.py      # Corpus analysis tools
│   │   ├── simple_analyzer.py      # Lightweight analyzer
│   │   ├── preprocessor.py         # Data preprocessing
│   │   ├── tokenizer_trainer.py    # Tokenizer training
│   │   └── dataset.py              # PyTorch datasets
│   ├── models/
│   │   └── bidirectional_mt.py     # Model architecture
│   ├── training/
│   │   └── trainer.py              # Training infrastructure
│   ├── evaluation/
│   │   └── metrics.py              # Evaluation metrics
│   └── inference/
│       └── translator.py           # Translation pipeline
├── data/
│   └── processed/                  # Processed corpus data
├── models/
│   └── tokenizer/                  # Trained tokenizer
├── checkpoints/                    # Model checkpoints
├── train_model.py                  # Main training script
├── translate_cli.py                # CLI interface
├── train_tokenizer_simple.py      # Simple tokenizer training
└── README.md                       # This file
```

## Evaluation Metrics

The system uses multiple evaluation metrics appropriate for morphologically rich languages:

### BLEU Score
- Standard MT evaluation metric
- N-gram precision with brevity penalty
- Reported for n=1,2,3,4

### chrF++ Score
- Character-level F-score
- Better for morphologically rich languages
- Considers character n-grams up to n=6

### Additional Metrics
- Length ratio analysis
- Inference speed (tokens/second)
- Translation quality assessment

## Performance

### Expected Results
Based on similar low-resource MT systems:

| Direction | BLEU | chrF++ | Notes |
|-----------|------|--------|-------|
| EN → IU | 20-25 | 40-45 | Legislative domain |
| IU → EN | 25-30 | 45-50 | Higher due to morphology |

### Inference Speed
- CPU: ~50-100 tokens/second
- GPU: ~200-500 tokens/second
- Batch processing: 2-5x speedup

## Usage Examples

### Basic Translation
```bash
# English to Inuktitut
python translate_cli.py -t "The government is working hard." -d en2iu

# Inuktitut to English  
python translate_cli.py -t "ᒐᕙᒪᒃᑯᑦ ᐊᔪᙱᓐᓂᖅᓴᐅᔪᒥᒃ ᐱᓕᕆᐊᖃᖅᑐᑦ." -d iu2en
```

### File Processing
```bash
# Translate entire file
python translate_cli.py -i speeches.txt -o translations.json -d en2iu
```

### Interactive Mode
```bash
# Start interactive session
python translate_cli.py --interactive -d en2iu
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is released under the MIT License. See LICENSE file for details.

## Citation

If you use this system in your research, please cite:

```bibtex
@software{nunavut_mt_2024,
  title={Bidirectional English-Inuktitut Machine Translation System},
  author={Your Name},
  year={2024},
  url={https://github.com/yourusername/nunavut-mt}
}
```

## Acknowledgments

- **Nunavut Hansard Corpus**: Thanks to the creators of the Nunavut Hansard Inuktitut-English Parallel Corpus 3.0
- **Indigenous Language Technology**: This work contributes to preserving and promoting Indigenous languages
- **Open Source Community**: Built using open-source tools and libraries

## Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Contact: [<EMAIL>]
- Documentation: See `docs/` directory for detailed guides

---

**Note**: This is a research system designed for educational and research purposes. For production use in sensitive contexts, additional validation and testing are recommended.
