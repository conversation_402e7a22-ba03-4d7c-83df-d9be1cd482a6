"""
Simple tokenizer training script for the Nunavut Hansard corpus.
"""

import sentencepiece as spm
import os
from pathlib import Path

def train_tokenizer():
    """Train a SentencePiece tokenizer on the processed corpus."""
    
    # Paths
    data_dir = Path("nunavut_mt/data/processed")
    output_dir = Path("nunavut_mt/models/tokenizer")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Prepare training data
    training_file = output_dir / "training_data.txt"
    
    print("Preparing training data...")
    with open(training_file, 'w', encoding='utf-8') as fout:
        # Add English data
        en_file = data_dir / "train.en"
        if en_file.exists():
            print(f"Adding English data from {en_file}")
            with open(en_file, 'r', encoding='utf-8') as fin:
                for i, line in enumerate(fin):
                    if i >= 100000:  # Limit for faster training
                        break
                    line = line.strip()
                    if line:
                        fout.write(line + '\n')
        
        # Add Inuktitut data
        iu_file = data_dir / "train.iu"
        if iu_file.exists():
            print(f"Adding Inuktitut data from {iu_file}")
            with open(iu_file, 'r', encoding='utf-8') as fin:
                for i, line in enumerate(fin):
                    if i >= 100000:  # Limit for faster training
                        break
                    line = line.strip()
                    if line:
                        fout.write(line + '\n')
    
    # Train tokenizer
    vocab_size = 16000  # Smaller vocab for faster training
    model_prefix = output_dir / f"tokenizer_v{vocab_size}"
    
    print(f"Training SentencePiece tokenizer with vocab_size={vocab_size}")
    
    spm.SentencePieceTrainer.train(
        input=str(training_file),
        model_prefix=str(model_prefix),
        vocab_size=vocab_size,
        model_type='bpe',
        character_coverage=0.9995,
        split_by_unicode_script=True,
        split_by_whitespace=True,
        normalization_rule_name='nfkc',
        remove_extra_whitespaces=True,
        user_defined_symbols='<en2iu>,<iu2en>',
        pad_id=0,
        unk_id=1,
        bos_id=2,
        eos_id=3
    )
    
    model_path = f"{model_prefix}.model"
    print(f"Tokenizer training complete! Model saved to: {model_path}")
    
    # Test the tokenizer
    print("\nTesting tokenizer...")
    sp = spm.SentencePieceProcessor()
    sp.load(model_path)
    
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "ᖁᔭᓐᓇᒦᒃ, ᐃᒃᓯᕙᐅᑖᖅ.",
        "<en2iu> Hello, how are you?",
        "<iu2en> ᐊᐃ, ᖃᓄᐃᓕᖓᕙ?"
    ]
    
    for sentence in test_sentences:
        tokens = sp.encode_as_pieces(sentence)
        print(f"'{sentence}' -> {tokens}")
    
    return model_path

if __name__ == "__main__":
    train_tokenizer()
