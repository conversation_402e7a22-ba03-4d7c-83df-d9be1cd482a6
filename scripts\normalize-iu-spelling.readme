                Notes on spelling normalization in Inuktitut
                     by <PERSON> and <PERSON>
                      National Research Council Canada
                                January 2020

Spelling of Inuktitut text has a number of variants that change over time and
geography. Different communities and dialects may employ different spelling
conventions and use either syllabics (qaniujaaqpait) or roman orthography
(qaliujaaqpait). There have been proposed orthographic standards, such as
those produced by the Inuit Cultural Institute in 1976, but there are still
discussions today about how and whether to standardize orthography across
dialects and communities. Although we don't pretend to know how to correctly
normalize IU spelling, there are a few simple rules that are reliable and
unambiguous and can be safely applied systematically. (We use the term
"normalize" in the natural language processing sense of increasing
consistency within our data set, not as a judgment on the correctness of any
particular orthographic conventions.) We provide the script
normalize-iu-spelling.pl to systematically apply those safe rules. Most of
these rules convert visually similar spelling variants (where the variation
may not be immediately evident to the eye in some fonts) into a single
consistent form for natural language processing systems to recognize.

We did not apply these normalization rules to the corpus, instead we provide
this script so users of the corpus can apply them if desired.  This allows us
to leave the underlying corpus unchanged, allowing for flexibility in case of
future changes in orthographic systems.

Note to the reader: this readme contains Inuktitut syllabics (UTF-8). When
these are viewed in a monospace font (as in many terminals), characters
may appear to overlap or be cut off. We recommend reading in a tool that
allows for variable-width display of characters (Notepad++, various word
processors, etc.).

References:
 - the "Quirky Characters" section at the bottom of the "Syllabics
   (qaniujaaqpait)" page on Pirurvik's Inuktut Tusaalanga web site:
   https://tusaalanga.ca/node/2506
 - Inuktitut syllabics on Wikipedia
   https://en.wikipedia.org/wiki/Inuktitut_syllabics
   (note: this page covers a wider range of Inuktitut dialects)
 - Inuktitut Linguistics for Technocrats by Mick Mallon
   http://www.inuktitutcomputing.ca/Technocrats/ILFT.php

Most rules below are implied by the spelling recommendations of the Inuktut
Tusaalanga page cited above.

1) ᕐ+ᑭ vs ᕿ (r+k vs q)

Tusaalanga warns to always spell rk with the single character. A Pirurvik
representative confirmed that this rule is solid for Inuktitut words, although
it is more arbitrary for foreign names translated into Inuktitut syllabics.
For example, the last name of John Quirke, clerk of the Legislative Assembly of
Nunavut, has a variety of transliterations, with ᑯᐊᕐᒃ (kuark) being common.
This rule will unfortunately change it to ᑯᐊᖅ (kuaq), but that spelling is also
reasonable, and visually difficult to distinguish in proportional fonts.

This translates to these four systematic substitutions as rule 1 in
normalize-iu-spelling.pl:
   ᕐ + ᑭ -> ᕿ
   ᕐ + ᑯ -> ᖁ
   ᕐ + ᑲ -> ᖃ
   ᕐ + ᒃ -> ᖅ

2) Variants for ᖏ (ng)

Again, Tusaalanga recommends the systematic use of the combined character. This
one occurs with three variants in the Nunavut Hansard corpus: ᖏ (ng), ᓐ+ᒋ (n+g)
and ᖕ + ᒋ (ng+g). Rule 2 substitutes the latter two by the former, and does so
for the 4 variants of ng (final and each of the three vowel variants).

3) Variants for doubled ᖏ (ng), which should be ᙱ (nng)

This one can occur in even more variants, listed in transliteration only for
simplicity: n+ng, ng+ng, n+n+g, n+ng+g, ng+n+g, ng+ng+g, where all but the last
character is always the final variant (without vowel), while the last character
can be with or without a vowel.

Rule 3: substitute all those by the appropriate single character variant.

4) Doubled ᕿ (q), which Tusaalanga says should be written ᖅ+ᑭ (q+k).

ᕐᒃᑭ (r+k+k) and other variants starting with ᕐᒃ (r+k) are already correctly
changed to ᖅᑭ (qk) by Rule 1 above.
Therefore, Rule 4 only needs to handle ᖅᕿ (q+q) and its other vowel variants.
Unlike the previous rules, we don't touch the final variant, since ᖅᖅ (qq
final) and ᖅᒃ (qk final) are both technically invalid letter sequences in
Inuktitut. Words that include them might be misspelled, or transliterations
from other languages, and there is no reliable systematic rule to handle them.

A further note on consecutive finals (syllabic characters representing a
consonant without a vowel): technically, an Inuktitut word should never contain
two consecutive finals. Yet, there are 18k instances of consecutive finals in
the Nunavut Hansard 3.0 corpus, covering 156 distinct pairs of the 196 pairs
that are theoretically possible. We investigated to find out why. Most examples
are from transliteration of words from languages with more consonant clusters
such as transliterated English names, e.g., Herb (Hᐆᕐᕝ), Barb (ᕚᕐᕝ), Hart
(ᕼᐋᕐᑦ), Donald (ᑖᓄᓪᑦ), Ralph (ᕌᓪᕝ), Quirke (ᑯᐊᕐᒃ). Except for the last one (and
other names with ᕐᒃ/rk), the normalize-iu-spelling.pl script leaves them as is.

5) H vs ᕼ

For H (ASCII) vs ᕼ (syllabics), the Hansard corpus uses both, but the syllabic
one is more common. There are many ambiguous cases, when either is followed by
a hyphen or an apostrophe-like character, but two cases can safely be
systematically normalized:
 - Rule 5a: followed by a syllabic character, it should be the syllabic ᕼ
   (e.g., ᕼᐊᓐᑐ ᑑᑑ, Hunter Tootoo - most instances are transliterated English
   names);
 - Rule 5b: followed by another ASCII letter, it should be the ASCII H
   (examples of non-standard syllabic ones in the corpus: ᕼAP program, ᕼelena,
   ᕼodgson Trophy).
Some fonts render these characters near-identically, so the difference is not
of concern to individuals reading the text, as it reads fluently either way.
For our automatic systems, however, the difference could cause difficulties.


                       Other notes on normalization.

There are many other characters that could benefit from normalization in the
corpus: apostrophes, quotation marks, various dashes, and syllabic characters
that are not part of Inuktitut syllabics. The intent of
normalize-iu-spelling.pl is to be very conservative and only apply safe rules.
However, in our machine translation pipeline, we use more aggressive (and
potentially less safe) set of normalizations for both syllabics and roman
script experiments. See, in particular, normalize-apos-romanized.pl and
normalize-apos-syllabics.pl. The remainder of this section applies only to
preprocessing for our machine translation experiments.

In normalize-apos-romanized.pl, we do three main types of normalization
(increasing data set consistency) for the purposes of machine translation of
Inuktitut after conversion from syllabics to roman orthography. The process
of conversion is multi-step (see preprocess-romanized.sh), consisting of a
conversion with uniconv (syllabics to roman), a second conversion with iconv
(to correct UTF-8 errors, e.g., on accented French characters), and finally
the hand-engineered normalize-apos-romanized.pl script.

The three types of normalization in normalize-apos-romanized.pl are:
 - Apostrophe normalization: handling of some apostrophe types outside of
   those normalized in Moses preprocessing scripts. This includes converting
   word-internal apostrophes to MODIFIER LETTER APOSTROPHE U+02BC, which is
   not segmented by the Moses tokenizer. This is done because the apostrophe
   character can be used to indicate a glottal stop, which should be treated
   as part of the word.
 - Correction of encoding artifacts generated by uniconv and iconv.
 - Correction of encoding artifacts specifically for syllabic characters
   outside of the standard set of Inuktitut syllabics. We do not attempt to
   convert those to Inuktitut syllabics even if there appears to be a
   correspondence.

In normalize-apos-syllabics.pl, we only perform apostrophe and quotation
normalization (as there are no additional encoding artifacts to resolve).


       Notes on romanization, as it relates to spelling and syllabics

General note: the following discussion assumes romanization using uniconv, as
described in the Nunavut Hansard 3.0 top-level README and discussed above.

The Nunavut Hansard 1.0 and 1.1 releases were released already romanized, and
the syllabics were not released with the data. In preparing this corpus and
running experiments (including work with Jeffrey Micher on morphological
processing), we noted several differences in romanization between versions
1.0/1.1 (v1) and our 3.0 release (v3). To the extent possible, we performed
the same romanization as it was described in the papers presenting the
earlier versions, so some of this may be due to changes in libraries in the
intervening time period.

LH vs & romanization:

We observed that there are Romanization differences between v1 and v3 of the
Nunavut Hansard Parallel Corpus. The most notable is "ᖤ" (and its variants),
which is transliterated in v1 as "&" and in v3 as "lh" – if needed, it is
simple to re-romanize with "&" instead of "lh", though this requires additional
caution in subsequent tokenization. It is not, however, a one-to-one mapping,
so converting all "lh" in the romanized data into "&" is not guaranteed to be
correct. The following table shows examples of why this is not the case,
primarily from English names containing "lh" being transcribed into Inuktitut
using "ᓪᕼ" ("l" followed by "h"). Sometimes this is also done using the roman
"H", which results in a capital "H" in the romanized output.

Syllabics   V3 Romanization   English      Notes
ᕖᔪᓪᕼᐊᐅᔅ     viijulhaus        Wheelhouse   Surname, syllabics spelled l+h
Dalhousie   Dalhousie         Dalhousie    roman text in syllabics data
ᑎᐅᓪHᐊᐅᓯ     tiulHausi         Dalhousie    roman H in syllabics word
ᑖᓪᕼᐊᐅᓯ      taalhausi         Dalhousie    syllabics spelled l+h
ᒥᐅᓪᕼᐊᐃᕙᓐ    miulhaivan        Millhaven    syllabics spelled l+h

NNG vs NGNG romanization:

There are some romanization difference regarding nng/ngng between v1 and v3,
which are additionally complicated by the spelling variation described earlier.
Without access to the syllabics-based version of the earlier corpora, we cannot
confirm with complete certainty whether these differences are the result of
differences in romanization, differences in the original syllabics, or from the
application of some sort of normalization process on the original or romanized
data. If we observe words outside of their parallel context, we may also
misinterpret spelling differences as romanization inconsistencies.

We know there are inconsistencies between the corpora, though, because we can
produce examples such as the following. (We highlight the difference using the
symbol ^ below the romanized characters of interest.) In this example, the v3
romanization produces "nnggaa", while v1 produced "nngaa". Applying the
normalize-iu-spelling.pl script before v3 romanization would have resulted in
the same romanization as we see in v1.

V1:
 - He has indicated that this gasoline came from New York I think, or somewhere in his statement.
 - uqaqsimammat uqsualuk niu juak-minngaalauriaksanganit iinguqquuqtuilaak, uvvaluunniit nanikiaq uqausirilauqtangani.
                                    ^^^^^
V3:
 - He has indicated that this gasoline came from New York I think, or somewhere in his statement.
 - uqaqsimammat uqsualuk niu juak-minnggaalauriaksanganit iinguqquuqtuilaak, uvvaluunniit nanikiaq uqausirilauqtangani.
                                    ^^^^^^
 - ᐅᖃᖅᓯᒪᒻᒪᑦ ᐅᖅᓱᐊᓗᒃ ᓂᐅ ᔪᐊᒃ-ᒥᖖᒑᓚᐅᕆᐊᒃᓴᖓᓂᑦ ᐄᖑᖅᑰᖅᑐᐃᓛᒃ, ᐅᕝᕙᓘᓐᓃᑦ ᓇᓂᑭᐊᖅ ᐅᖃᐅᓯᕆᓚᐅᖅᑕᖓᓂ.
                           ^^

Q vs G romanization:

Assuming that the syllabics remained consistent between v1 and v3, there is a
difference in the romanization of ᖄ between the two versions. This is evidenced
by the middle of the second Inuktitut word in the following example. We do not
yet understand the reason for this romanization difference.

V1:
 - It is that heating stove, it is so old, I felt that it was unsafe.
 - tainnailaak igatugaaluk uqquujautigijaujuq pituqallarialuungmat, attanarasugilaurakkuli.
                    ^^^
V3:
 - It is that heating stove, it is so old, I felt that it was unsafe.
 - tainnailaak igatuqaaluk uqquujautigijaujuq pituqallarialuungmat, attanarasugilaurakkuli.
                    ^^^
 - ᑕᐃᓐᓇᐃᓛᒃ ᐃᒐᑐᖄᓗᒃ ᐅᖅᑰᔭᐅᑎᒋᔭᐅᔪᖅ ᐱᑐᖃᓪᓚᕆᐊᓘᖕᒪᑦ, ᐊᑦᑕᓇᕋᓱᒋᓚᐅᕋᒃᑯᓕ.
              ^

Other romanization differences:

There may also be some other differences that are not listed here – through
manual examination, we found some possible examples of v1 "ik" becoming v3
"i", or "ut" becoming "ik" – it is not clear that those (or the ones shown
above) are resolvable without context. For researchers using the v3 corpus as
is, none of these romanization issues pose a major concern. It remains an open
question how much of an effect these romanization differences have on things
like a morphological analyzer trained on v1 and then applied to v3,
particularly given that all of these examples may be at least somewhat
ambiguous to resolve. As it stands, we stumbled into many of these
inconsistencies through luck and manual examination of a dataset that is too
large to manually analyze. If one wanted to do a very thorough analysis of
this, one could run alignment between v1 and the start of v3 (English-English
would likely be easiest); for any aligned sentences, run word alignment
(Inuktitut-Inuktitut, romanized, with the syllabics available for additional
information) and then analyze any that are not exact matches.
There is lots of potential future work here!


Acknowledgements

We would like to thank our colleagues for assistance and discussion. In
particular, Darlene Stewart helped examine differences between the v1 and v3
romanizations and Jeffrey Micher provided expertise on differences based on his
knowledge of morphological analyzers built from the earlier corpora.
