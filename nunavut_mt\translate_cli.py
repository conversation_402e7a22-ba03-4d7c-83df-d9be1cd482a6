#!/usr/bin/env python3
"""
Command-line interface for the bidirectional English-Inuktitut translator.

This script provides an easy-to-use CLI for translating text between
English and Inuktitut in both directions.
"""

import argparse
import sys
import json
from pathlib import Path
import logging

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from inference.translator import TranslationPipeline
except ImportError:
    print("Error: Could not import translation modules.")
    print("Make sure you're running from the correct directory.")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Bidirectional English-Inuktitut Machine Translation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Translate a single sentence
  python translate_cli.py --text "Thank you, Mr. Speaker." --direction en2iu
  
  # Translate from file
  python translate_cli.py --input input.txt --output output.json --direction en2iu
  
  # Interactive mode
  python translate_cli.py --interactive --direction en2iu
  
  # Translate both directions
  python translate_cli.py --text "Hello" --direction both
        """
    )
    
    # Input options (mutually exclusive)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--text", "-t", type=str,
                           help="Text to translate")
    input_group.add_argument("--input", "-i", type=str,
                           help="Input file containing text to translate (one sentence per line)")
    input_group.add_argument("--interactive", action="store_true",
                           help="Interactive translation mode")
    
    # Translation direction
    parser.add_argument("--direction", "-d", type=str, 
                       choices=["en2iu", "iu2en", "both"], default="en2iu",
                       help="Translation direction (default: en2iu)")
    
    # Output options
    parser.add_argument("--output", "-o", type=str,
                       help="Output file for results (JSON format)")
    parser.add_argument("--format", "-f", type=str, 
                       choices=["text", "json"], default="text",
                       help="Output format (default: text)")
    
    # Model options
    parser.add_argument("--tokenizer", type=str, 
                       default="nunavut_mt/models/tokenizer/tokenizer_v16000.model",
                       help="Path to tokenizer model")
    parser.add_argument("--device", type=str, default="cpu",
                       help="Device to use for inference (cpu, cuda)")
    
    # Other options
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    parser.add_argument("--quiet", "-q", action="store_true",
                       help="Quiet mode (minimal output)")
    
    return parser.parse_args()


def setup_logging(verbose: bool, quiet: bool):
    """Setup logging based on verbosity flags."""
    if quiet:
        logging.getLogger().setLevel(logging.ERROR)
    elif verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)


def translate_single_text(pipeline: TranslationPipeline, text: str, direction: str, format_type: str):
    """Translate a single text and print results."""
    if direction == "both":
        # Translate in both directions
        directions = ["en2iu", "iu2en"]
        results = []
        
        for dir in directions:
            result = pipeline.translate_text(text, dir)
            results.append(result)
            
            if format_type == "text":
                dir_name = "English → Inuktitut" if dir == "en2iu" else "Inuktitut → English"
                print(f"\n{dir_name}:")
                print(f"Input:  {result['input_text']}")
                print(f"Output: {result['translation']}")
                print(f"Time:   {result['inference_time']:.3f}s")
        
        if format_type == "json":
            print(json.dumps(results, indent=2, ensure_ascii=False))
    
    else:
        # Single direction
        result = pipeline.translate_text(text, direction)
        
        if format_type == "text":
            dir_name = "English → Inuktitut" if direction == "en2iu" else "Inuktitut → English"
            print(f"\n{dir_name}:")
            print(f"Input:  {result['input_text']}")
            print(f"Output: {result['translation']}")
            print(f"Time:   {result['inference_time']:.3f}s")
        else:
            print(json.dumps(result, indent=2, ensure_ascii=False))


def translate_file(pipeline: TranslationPipeline, input_file: str, output_file: str, direction: str):
    """Translate text from file."""
    try:
        results, batch_stats = pipeline.translate_file(input_file, output_file, direction)
        
        print(f"Translation completed!")
        print(f"Input file: {input_file}")
        print(f"Output file: {output_file}")
        print(f"Sentences translated: {batch_stats['batch_size']}")
        print(f"Total time: {batch_stats['total_time']:.2f}s")
        print(f"Average time per sentence: {batch_stats['avg_time_per_sentence']:.3f}s")
        
    except FileNotFoundError as e:
        logger.error(f"File not found: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during file translation: {e}")
        sys.exit(1)


def interactive_mode(pipeline: TranslationPipeline, direction: str):
    """Interactive translation mode."""
    print("=" * 60)
    print("Interactive English-Inuktitut Translation")
    print("=" * 60)
    
    if direction == "en2iu":
        print("Mode: English → Inuktitut")
    elif direction == "iu2en":
        print("Mode: Inuktitut → English")
    else:
        print("Mode: Bidirectional (both directions)")
    
    print("Type 'quit' or 'exit' to stop, 'help' for commands")
    print("-" * 60)
    
    while True:
        try:
            # Get input
            if direction == "en2iu":
                text = input("EN> ").strip()
            elif direction == "iu2en":
                text = input("IU> ").strip()
            else:
                text = input(">> ").strip()
            
            # Handle commands
            if text.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            elif text.lower() in ['help', 'h']:
                print("Commands:")
                print("  quit, exit, q  - Exit the program")
                print("  help, h        - Show this help")
                print("  clear, c       - Clear screen")
                print("  switch, s      - Switch translation direction (if both mode)")
                continue
            elif text.lower() in ['clear', 'c']:
                import os
                os.system('cls' if os.name == 'nt' else 'clear')
                continue
            elif text.lower() in ['switch', 's'] and direction == "both":
                # Toggle between directions (simplified)
                print("Direction switching not implemented in both mode")
                continue
            elif not text:
                continue
            
            # Translate
            translate_single_text(pipeline, text, direction, "text")
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            logger.error(f"Translation error: {e}")


def main():
    """Main CLI function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(args.verbose, args.quiet)
    
    # Check tokenizer file
    tokenizer_path = Path(args.tokenizer)
    if not tokenizer_path.exists():
        logger.error(f"Tokenizer not found: {tokenizer_path}")
        logger.error("Please train the tokenizer first or provide correct path")
        sys.exit(1)
    
    # Initialize translation pipeline
    try:
        logger.info(f"Loading tokenizer: {tokenizer_path}")
        pipeline = TranslationPipeline(str(tokenizer_path), args.device)
        logger.info("Translation pipeline initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize translation pipeline: {e}")
        sys.exit(1)
    
    # Handle different modes
    if args.text:
        # Single text translation
        translate_single_text(pipeline, args.text, args.direction, args.format)
        
    elif args.input:
        # File translation
        if not args.output:
            # Generate output filename
            input_path = Path(args.input)
            args.output = str(input_path.with_suffix('.json'))
        
        translate_file(pipeline, args.input, args.output, args.direction)
        
    elif args.interactive:
        # Interactive mode
        interactive_mode(pipeline, args.direction)
    
    logger.info("Translation completed successfully")


if __name__ == "__main__":
    main()
