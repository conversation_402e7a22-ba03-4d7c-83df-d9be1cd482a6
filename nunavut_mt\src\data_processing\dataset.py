"""
Dataset classes for bidirectional machine translation training.

This module provides PyTorch dataset classes for loading and processing
the Nunavut Hansard parallel corpus for training.
"""

import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import random
import logging

logger = logging.getLogger(__name__)


class BilingualDataset(Dataset):
    """Dataset for bidirectional machine translation."""
    
    def __init__(self, 
                 en_file: str, 
                 iu_file: str,
                 tokenizer,
                 max_length: int = 512,
                 bidirectional: bool = True):
        """
        Initialize the bilingual dataset.
        
        Args:
            en_file: Path to English text file
            iu_file: Path to Inuktitut text file
            tokenizer: Bilingual tokenizer instance
            max_length: Maximum sequence length
            bidirectional: Whether to include both translation directions
        """
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.bidirectional = bidirectional
        
        # Load parallel sentences
        self.en_sentences = self._load_sentences(en_file)
        self.iu_sentences = self._load_sentences(iu_file)
        
        assert len(self.en_sentences) == len(self.iu_sentences), \
            f"Mismatched lengths: EN={len(self.en_sentences)}, IU={len(self.iu_sentences)}"
        
        # Create training examples
        self.examples = self._create_examples()
        
        logger.info(f"Loaded {len(self.examples)} training examples")
        logger.info(f"Bidirectional training: {bidirectional}")
    
    def _load_sentences(self, file_path: str) -> List[str]:
        """Load sentences from file."""
        sentences = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:  # Skip empty lines
                    sentences.append(line)
        return sentences
    
    def _create_examples(self) -> List[Dict]:
        """Create training examples with direction information."""
        examples = []
        
        for en_sent, iu_sent in zip(self.en_sentences, self.iu_sentences):
            # Skip empty sentences
            if not en_sent.strip() or not iu_sent.strip():
                continue
            
            # English to Inuktitut
            examples.append({
                'source': en_sent,
                'target': iu_sent,
                'direction': 'en2iu'
            })
            
            # Inuktitut to English (if bidirectional)
            if self.bidirectional:
                examples.append({
                    'source': iu_sent,
                    'target': en_sent,
                    'direction': 'iu2en'
                })
        
        return examples
    
    def __len__(self) -> int:
        return len(self.examples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a training example."""
        example = self.examples[idx]
        
        # Encode source with direction token
        source_ids = self.tokenizer.encode(example['source'], example['direction'])
        
        # Encode target (without direction token)
        target_ids = self.tokenizer.encode(example['target'], example['direction'])
        # Remove the direction token from target (it's only needed for source)
        if len(target_ids) > 1:
            target_ids = [target_ids[0]] + target_ids[2:]  # Keep BOS, skip direction token
        
        # Truncate if too long
        if len(source_ids) > self.max_length:
            source_ids = source_ids[:self.max_length-1] + [self.tokenizer.eos_token_id]
        
        if len(target_ids) > self.max_length:
            target_ids = target_ids[:self.max_length-1] + [self.tokenizer.eos_token_id]
        
        return {
            'source_ids': torch.tensor(source_ids, dtype=torch.long),
            'target_ids': torch.tensor(target_ids, dtype=torch.long),
            'direction': example['direction']
        }


class DataCollator:
    """Data collator for batching training examples."""
    
    def __init__(self, tokenizer, pad_to_multiple_of: Optional[int] = None):
        """
        Initialize the data collator.
        
        Args:
            tokenizer: Bilingual tokenizer instance
            pad_to_multiple_of: Pad to multiple of this value
        """
        self.tokenizer = tokenizer
        self.pad_to_multiple_of = pad_to_multiple_of
    
    def __call__(self, batch: List[Dict]) -> Dict[str, torch.Tensor]:
        """Collate a batch of examples."""
        # Extract sequences
        source_ids = [example['source_ids'] for example in batch]
        target_ids = [example['target_ids'] for example in batch]
        
        # Pad sequences
        source_batch = self._pad_sequences(source_ids)
        target_batch = self._pad_sequences(target_ids)
        
        # Create labels (target_ids shifted for language modeling)
        labels = target_batch['input_ids'].clone()
        labels[labels == self.tokenizer.pad_token_id] = -100  # Ignore padding in loss
        
        return {
            'input_ids': source_batch['input_ids'],
            'attention_mask': source_batch['attention_mask'],
            'labels': labels
        }
    
    def _pad_sequences(self, sequences: List[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Pad sequences to the same length."""
        # Find max length
        max_len = max(len(seq) for seq in sequences)
        
        # Apply padding multiple constraint
        if self.pad_to_multiple_of:
            max_len = ((max_len + self.pad_to_multiple_of - 1) // self.pad_to_multiple_of) * self.pad_to_multiple_of
        
        # Pad sequences
        input_ids = []
        attention_mask = []
        
        for seq in sequences:
            seq_len = len(seq)
            pad_len = max_len - seq_len
            
            # Pad input_ids
            padded_seq = torch.cat([seq, torch.full((pad_len,), self.tokenizer.pad_token_id, dtype=torch.long)])
            input_ids.append(padded_seq)
            
            # Create attention mask
            mask = torch.cat([torch.ones(seq_len, dtype=torch.long), torch.zeros(pad_len, dtype=torch.long)])
            attention_mask.append(mask)
        
        return {
            'input_ids': torch.stack(input_ids),
            'attention_mask': torch.stack(attention_mask)
        }


def create_dataloaders(data_dir: str, 
                      tokenizer,
                      batch_size: int = 16,
                      max_length: int = 512,
                      num_workers: int = 4,
                      bidirectional: bool = True) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create data loaders for training, validation, and testing.
    
    Args:
        data_dir: Directory containing processed data files
        tokenizer: Bilingual tokenizer instance
        batch_size: Batch size for training
        max_length: Maximum sequence length
        num_workers: Number of worker processes
        bidirectional: Whether to use bidirectional training
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_path = Path(data_dir)
    
    # Create datasets
    train_dataset = BilingualDataset(
        en_file=data_path / "train.en",
        iu_file=data_path / "train.iu",
        tokenizer=tokenizer,
        max_length=max_length,
        bidirectional=bidirectional
    )
    
    val_dataset = BilingualDataset(
        en_file=data_path / "dev-dedup.en",
        iu_file=data_path / "dev-dedup.iu",
        tokenizer=tokenizer,
        max_length=max_length,
        bidirectional=bidirectional
    )
    
    test_dataset = BilingualDataset(
        en_file=data_path / "test-dedup.en",
        iu_file=data_path / "test-dedup.iu",
        tokenizer=tokenizer,
        max_length=max_length,
        bidirectional=bidirectional
    )
    
    # Create data collator
    collator = DataCollator(tokenizer)
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        collate_fn=collator,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collator,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        collate_fn=collator,
        pin_memory=True
    )
    
    logger.info(f"Created data loaders:")
    logger.info(f"  Train: {len(train_dataset)} examples, {len(train_loader)} batches")
    logger.info(f"  Val: {len(val_dataset)} examples, {len(val_loader)} batches")
    logger.info(f"  Test: {len(test_dataset)} examples, {len(test_loader)} batches")
    
    return train_loader, val_loader, test_loader


class InferenceDataset(Dataset):
    """Dataset for inference/translation."""
    
    def __init__(self, sentences: List[str], tokenizer, direction: str = "en2iu", max_length: int = 512):
        """
        Initialize inference dataset.
        
        Args:
            sentences: List of sentences to translate
            tokenizer: Bilingual tokenizer instance
            direction: Translation direction
            max_length: Maximum sequence length
        """
        self.sentences = sentences
        self.tokenizer = tokenizer
        self.direction = direction
        self.max_length = max_length
    
    def __len__(self) -> int:
        return len(self.sentences)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get an inference example."""
        sentence = self.sentences[idx]
        
        # Encode with direction token
        source_ids = self.tokenizer.encode(sentence, self.direction)
        
        # Truncate if too long
        if len(source_ids) > self.max_length:
            source_ids = source_ids[:self.max_length-1] + [self.tokenizer.eos_token_id]
        
        return {
            'source_ids': torch.tensor(source_ids, dtype=torch.long),
            'original_text': sentence
        }


def create_inference_loader(sentences: List[str], 
                          tokenizer,
                          direction: str = "en2iu",
                          batch_size: int = 32,
                          max_length: int = 512) -> DataLoader:
    """
    Create data loader for inference.
    
    Args:
        sentences: List of sentences to translate
        tokenizer: Bilingual tokenizer instance
        direction: Translation direction
        batch_size: Batch size for inference
        max_length: Maximum sequence length
        
    Returns:
        DataLoader for inference
    """
    dataset = InferenceDataset(sentences, tokenizer, direction, max_length)
    
    def collate_fn(batch):
        source_ids = [example['source_ids'] for example in batch]
        original_texts = [example['original_text'] for example in batch]
        
        # Pad sequences
        max_len = max(len(seq) for seq in source_ids)
        
        input_ids = []
        attention_mask = []
        
        for seq in source_ids:
            seq_len = len(seq)
            pad_len = max_len - seq_len
            
            padded_seq = torch.cat([seq, torch.full((pad_len,), tokenizer.pad_token_id, dtype=torch.long)])
            input_ids.append(padded_seq)
            
            mask = torch.cat([torch.ones(seq_len, dtype=torch.long), torch.zeros(pad_len, dtype=torch.long)])
            attention_mask.append(mask)
        
        return {
            'input_ids': torch.stack(input_ids),
            'attention_mask': torch.stack(attention_mask),
            'original_texts': original_texts
        }
    
    return DataLoader(dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)


if __name__ == "__main__":
    # Example usage
    from src.models.bidirectional_mt import BilingualTokenizer
    
    # Initialize tokenizer
    tokenizer = BilingualTokenizer("nunavut_mt/models/tokenizer/tokenizer_v16000.model")
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_dataloaders(
        data_dir="nunavut_mt/data/processed",
        tokenizer=tokenizer,
        batch_size=8,
        bidirectional=True
    )
    
    # Test a batch
    batch = next(iter(train_loader))
    print("Batch keys:", batch.keys())
    print("Input shape:", batch['input_ids'].shape)
    print("Labels shape:", batch['labels'].shape)
    print("Attention mask shape:", batch['attention_mask'].shape)
    
    print("Dataset creation successful!")
