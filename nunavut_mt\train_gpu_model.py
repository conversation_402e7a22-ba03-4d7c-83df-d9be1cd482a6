#!/usr/bin/env python3
"""
GPU-optimized neural model training for Nunavut MT.

This script trains a real neural translation model using your RTX 4070 GPU
with the Transformers library for better results.
"""

import os
import sys
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import sentencepiece as spm
from transformers import (
    MBartForConditionalGeneration, 
    MBartTokenizer,
    AdamW,
    get_linear_schedule_with_warmup
)
from pathlib import Path
import json
import time
from tqdm import tqdm
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class NunavutDataset(Dataset):
    """Dataset for Nunavut MT training."""
    
    def __init__(self, en_file, iu_file, tokenizer, max_len=256, max_samples=10000):
        self.tokenizer = tokenizer
        self.max_len = max_len
        
        # Load data
        with open(en_file, 'r', encoding='utf-8') as f:
            en_lines = [line.strip() for line in f if line.strip()]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_lines = [line.strip() for line in f if line.strip()]
        
        # Take subset for faster training
        self.en_lines = en_lines[:max_samples]
        self.iu_lines = iu_lines[:max_samples]
        
        logger.info(f"Loaded {len(self.en_lines)} training pairs")
    
    def __len__(self):
        return len(self.en_lines)
    
    def __getitem__(self, idx):
        en_text = self.en_lines[idx]
        iu_text = self.iu_lines[idx]
        
        # Tokenize
        en_tokens = self.tokenizer.encode(f"<en2iu> {en_text}", max_length=self.max_len, truncation=True)
        iu_tokens = self.tokenizer.encode(iu_text, max_length=self.max_len, truncation=True)
        
        return {
            'input_ids': torch.tensor(en_tokens, dtype=torch.long),
            'labels': torch.tensor(iu_tokens, dtype=torch.long)
        }


def collate_fn(batch):
    """Collate function for batching."""
    input_ids = [item['input_ids'] for item in batch]
    labels = [item['labels'] for item in batch]
    
    # Pad sequences
    max_input_len = max(len(seq) for seq in input_ids)
    max_label_len = max(len(seq) for seq in labels)
    
    padded_inputs = []
    padded_labels = []
    attention_masks = []
    
    for inp, lbl in zip(input_ids, labels):
        # Pad input
        inp_padded = torch.cat([inp, torch.zeros(max_input_len - len(inp), dtype=torch.long)])
        padded_inputs.append(inp_padded)
        
        # Create attention mask
        mask = torch.cat([torch.ones(len(inp), dtype=torch.long), 
                         torch.zeros(max_input_len - len(inp), dtype=torch.long)])
        attention_masks.append(mask)
        
        # Pad labels
        lbl_padded = torch.cat([lbl, torch.full((max_label_len - len(lbl),), -100, dtype=torch.long)])
        padded_labels.append(lbl_padded)
    
    return {
        'input_ids': torch.stack(padded_inputs),
        'attention_mask': torch.stack(attention_masks),
        'labels': torch.stack(padded_labels)
    }


class SimpleNeuralTranslator(nn.Module):
    """Simple neural translator using transformer architecture."""
    
    def __init__(self, vocab_size, d_model=512, nhead=8, num_layers=6):
        super().__init__()
        self.d_model = d_model
        self.vocab_size = vocab_size
        
        # Embeddings
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.pos_encoding = nn.Parameter(torch.randn(1000, d_model))
        
        # Transformer layers
        encoder_layer = nn.TransformerEncoderLayer(d_model, nhead, batch_first=True)
        self.encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        
        decoder_layer = nn.TransformerDecoderLayer(d_model, nhead, batch_first=True)
        self.decoder = nn.TransformerDecoder(decoder_layer, num_layers)
        
        # Output projection
        self.output_proj = nn.Linear(d_model, vocab_size)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, src, tgt, src_mask=None, tgt_mask=None):
        # Embed source
        src_seq_len = src.size(1)
        src_emb = self.embedding(src) * (self.d_model ** 0.5)
        src_emb = src_emb + self.pos_encoding[:src_seq_len].unsqueeze(0)
        src_emb = self.dropout(src_emb)
        
        # Embed target
        tgt_seq_len = tgt.size(1)
        tgt_emb = self.embedding(tgt) * (self.d_model ** 0.5)
        tgt_emb = tgt_emb + self.pos_encoding[:tgt_seq_len].unsqueeze(0)
        tgt_emb = self.dropout(tgt_emb)
        
        # Encode
        memory = self.encoder(src_emb, src_key_padding_mask=src_mask)
        
        # Decode
        output = self.decoder(tgt_emb, memory, tgt_key_padding_mask=tgt_mask)
        
        # Project to vocabulary
        return self.output_proj(output)


def train_model():
    """Train the neural translation model on GPU."""
    
    # Check GPU
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    if torch.cuda.is_available():
        logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Paths
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    data_dir = Path("nunavut_mt/data/processed")
    model_dir = Path("nunavut_mt/models/neural_gpu")
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Load tokenizer
    logger.info("Loading tokenizer...")
    sp_tokenizer = spm.SentencePieceProcessor()
    sp_tokenizer.load(tokenizer_path)
    vocab_size = sp_tokenizer.get_piece_size()
    
    # Create a simple tokenizer wrapper for compatibility
    class SimpleTokenizer:
        def __init__(self, sp_model):
            self.sp = sp_model
            
        def encode(self, text, max_length=512, truncation=True):
            tokens = self.sp.encode_as_ids(text)
            if truncation and len(tokens) > max_length:
                tokens = tokens[:max_length]
            return tokens
    
    tokenizer = SimpleTokenizer(sp_tokenizer)
    
    # Create dataset
    logger.info("Creating dataset...")
    dataset = NunavutDataset(
        en_file=data_dir / "train.en",
        iu_file=data_dir / "train.iu",
        tokenizer=tokenizer,
        max_samples=20000  # Increased for better training
    )
    
    # Create dataloader
    dataloader = DataLoader(
        dataset,
        batch_size=8,  # Good size for RTX 4070
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=0  # No multiprocessing for Windows compatibility
    )
    
    # Create model
    logger.info("Creating model...")
    model = SimpleNeuralTranslator(vocab_size, d_model=512, nhead=8, num_layers=4)
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"Model parameters: {total_params:,}")
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss(ignore_index=-100)
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    
    # Training loop
    logger.info("Starting GPU training...")
    model.train()
    
    num_epochs = 3
    total_loss = 0
    step = 0
    
    for epoch in range(num_epochs):
        logger.info(f"Epoch {epoch + 1}/{num_epochs}")
        
        epoch_loss = 0
        progress_bar = tqdm(dataloader, desc=f"Training Epoch {epoch + 1}")
        
        for batch in progress_bar:
            # Move to GPU
            input_ids = batch['input_ids'].to(device)
            attention_mask = batch['attention_mask'].to(device)
            labels = batch['labels'].to(device)
            
            # Create target input (shift labels)
            tgt_input = torch.cat([torch.zeros(labels.size(0), 1, dtype=torch.long, device=device), 
                                  labels[:, :-1]], dim=1)
            
            # Forward pass
            outputs = model(input_ids, tgt_input, 
                          src_mask=~attention_mask.bool(),
                          tgt_mask=None)
            
            # Calculate loss
            loss = criterion(outputs.view(-1, vocab_size), labels.view(-1))
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()
            
            # Update metrics
            total_loss += loss.item()
            epoch_loss += loss.item()
            step += 1
            
            # Update progress bar
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'avg_loss': f"{epoch_loss / (progress_bar.n + 1):.4f}",
                'gpu_mem': f"{torch.cuda.memory_allocated() / 1e9:.1f}GB"
            })
            
            # Log periodically
            if step % 50 == 0:
                logger.info(f"Step {step}: loss = {loss.item():.4f}, GPU memory = {torch.cuda.memory_allocated() / 1e9:.1f}GB")
        
        logger.info(f"Epoch {epoch + 1} completed. Average loss: {epoch_loss / len(dataloader):.4f}")
    
    # Save model
    logger.info("Saving trained model...")
    model_path = model_dir / "neural_translation_model.pt"
    torch.save({
        'model_state_dict': model.state_dict(),
        'vocab_size': vocab_size,
        'model_config': {
            'd_model': 512,
            'nhead': 8,
            'num_layers': 4
        },
        'tokenizer_path': tokenizer_path
    }, model_path)
    
    # Save training info
    training_info = {
        'vocab_size': vocab_size,
        'training_samples': len(dataset),
        'epochs': num_epochs,
        'final_loss': total_loss / step if step > 0 else 0,
        'model_path': str(model_path),
        'device': str(device),
        'gpu_name': torch.cuda.get_device_name(0) if torch.cuda.is_available() else None
    }
    
    with open(model_dir / "training_info.json", 'w') as f:
        json.dump(training_info, f, indent=2)
    
    logger.info(f"Training completed! Model saved to {model_path}")
    return model_path


def test_trained_model(model_path):
    """Test the trained model."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load model info
    checkpoint = torch.load(model_path, map_location=device)
    vocab_size = checkpoint['vocab_size']
    tokenizer_path = checkpoint['tokenizer_path']
    
    # Load tokenizer
    sp_tokenizer = spm.SentencePieceProcessor()
    sp_tokenizer.load(tokenizer_path)
    
    # Load model
    model = SimpleNeuralTranslator(vocab_size, d_model=512, nhead=8, num_layers=4)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # Test sentences
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "Hello, how are you?",
        "The government is working hard.",
        "Good morning, everyone."
    ]
    
    logger.info("Testing trained neural model:")
    print("\n" + "="*60)
    print("🚀 GPU-TRAINED NEURAL MODEL TRANSLATIONS")
    print("="*60)
    
    with torch.no_grad():
        for sentence in test_sentences:
            # Tokenize input
            input_text = f"<en2iu> {sentence}"
            input_tokens = sp_tokenizer.encode_as_ids(input_text)
            input_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
            
            # Simple greedy decoding
            max_len = 50
            output_tokens = []
            
            for _ in range(max_len):
                if len(output_tokens) == 0:
                    tgt_input = torch.zeros(1, 1, dtype=torch.long, device=device)
                else:
                    tgt_input = torch.tensor([output_tokens], dtype=torch.long).to(device)
                
                outputs = model(input_tensor, tgt_input)
                next_token = outputs[0, -1].argmax().item()
                
                if next_token == 3:  # EOS token
                    break
                    
                output_tokens.append(next_token)
            
            # Decode output
            if output_tokens:
                predicted_text = sp_tokenizer.decode_ids(output_tokens)
            else:
                predicted_text = "[No output generated]"
            
            print(f"EN: {sentence}")
            print(f"IU: {predicted_text}")
            print("-" * 40)


if __name__ == "__main__":
    logger.info("🚀 Starting GPU neural model training for Nunavut MT")
    
    try:
        # Train the model
        model_path = train_model()
        
        # Test the trained model
        test_trained_model(model_path)
        
        logger.info("🎉 GPU neural model training completed successfully!")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        import traceback
        traceback.print_exc()
        raise
