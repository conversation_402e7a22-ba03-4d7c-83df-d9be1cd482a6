"""
Simple test script for the tokenizer without heavy dependencies.
"""

import sentencepiece as smp

def test_tokenizer():
    """Test the trained tokenizer."""
    
    # Load tokenizer
    sp = smp.SentencePieceProcessor()
    sp.load("nunavut_mt/models/tokenizer/tokenizer_v16000.model")
    
    print("Tokenizer loaded successfully!")
    print(f"Vocabulary size: {sp.get_piece_size()}")
    
    # Test special tokens
    en2iu_id = sp.piece_to_id("<en2iu>")
    iu2en_id = sp.piece_to_id("<iu2en>")
    
    print(f"Special token IDs: en2iu={en2iu_id}, iu2en={iu2en_id}")
    
    # Test encoding/decoding
    test_sentences = [
        "Thank you, Mr. Speaker.",
        "ᖁᔭᓐᓇᒦᒃ, ᐃᒃᓯᕙᐅᑖᖅ.",
        "<en2iu> Hello, how are you?",
        "<iu2en> ᐊᐃ, ᖃᓄᐃᓕᖓᕙ?"
    ]
    
    print("\nTesting tokenization:")
    for sentence in test_sentences:
        tokens = sp.encode_as_pieces(sentence)
        ids = sp.encode_as_ids(sentence)
        reconstructed = sp.decode_pieces(tokens)
        
        print(f"Original: {sentence}")
        print(f"Tokens: {tokens[:10]}{'...' if len(tokens) > 10 else ''}")
        print(f"IDs: {ids[:10]}{'...' if len(ids) > 10 else ''}")
        print(f"Reconstructed: {reconstructed}")
        print("-" * 40)
    
    return True

if __name__ == "__main__":
    test_tokenizer()
    print("Tokenizer test completed successfully!")
