@echo off
REM Setup script for Nunavut MT environment on Windows

echo ========================================
echo Setting up Nunavut MT Environment
echo ========================================

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Conda not found. Please install Anaconda or Miniconda first.
    echo Download from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo Step 1: Creating conda environment...
conda env create -f environment.yml

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Failed to create conda environment
    echo Trying alternative pip installation...
    goto pip_install
)

echo Step 2: Activating environment...
call conda activate nunavut-mt

echo Step 3: Verifying installation...
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
python -c "import sentencepiece; print('SentencePiece: OK')"

echo.
echo ========================================
echo Environment setup completed successfully!
echo ========================================
echo.
echo To activate the environment, run:
echo   conda activate nunavut-mt
echo.
echo To test the system, run:
echo   python nunavut_mt/translate_cli.py --text "Hello" --direction en2iu
echo.
goto end

:pip_install
echo.
echo ========================================
echo Alternative: Setting up with pip
echo ========================================

REM Create virtual environment
python -m venv nunavut_mt_env

REM Activate virtual environment
call nunavut_mt_env\Scripts\activate.bat

REM Upgrade pip
python -m pip install --upgrade pip

REM Install PyTorch first (CPU version for compatibility)
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu

REM Install other requirements
pip install -r requirements.txt

echo.
echo ========================================
echo Pip environment setup completed!
echo ========================================
echo.
echo To activate the environment, run:
echo   nunavut_mt_env\Scripts\activate.bat
echo.

:end
echo Setup complete! Check above for any errors.
pause
