#!/usr/bin/env python3
"""
Debug script to understand what's happening with the model.
"""

import torch
import sentencepiece as spm
from pathlib import Path
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def debug_model():
    """Debug the model to see what's happening."""
    
    print("🔍 Debugging Neural Model")
    print("=" * 40)
    
    # Load tokenizer
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    # Load model
    model_path = "nunavut_mt/models/improved_gpu/improved_translation_model.pt"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    from train_efficient_gpu import EfficientTranslator
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']
    
    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    print(f"✅ Model loaded: {vocab_size} vocab, {config}")
    
    # Test tokenizer
    print("\n🔤 Testing Tokenizer:")
    test_text = "<en2iu> Thank you"
    tokens = tokenizer.encode_as_ids(test_text)
    decoded = tokenizer.decode_ids(tokens)
    print(f"   Input: '{test_text}'")
    print(f"   Tokens: {tokens}")
    print(f"   Decoded: '{decoded}'")
    
    # Check special tokens
    print("\n🏷️  Special Tokens:")
    special_tokens = ['<en2iu>', '<iu2en>', '<s>', '</s>', '<unk>']
    for token in special_tokens:
        token_id = tokenizer.piece_to_id(token)
        print(f"   {token}: {token_id}")
    
    # Test model forward pass
    print("\n🧠 Testing Model Forward Pass:")
    
    with torch.no_grad():
        # Prepare input
        input_text = "<en2iu> Thank you"
        input_tokens = tokenizer.encode_as_ids(input_text)
        print(f"   Input tokens: {input_tokens}")
        
        src_tensor = torch.tensor([input_tokens], dtype=torch.long).to(device)
        print(f"   Source tensor shape: {src_tensor.shape}")
        
        # Test with simple target
        tgt_tokens = [2]  # BOS token
        tgt_tensor = torch.tensor([tgt_tokens], dtype=torch.long).to(device)
        print(f"   Target tensor shape: {tgt_tensor.shape}")
        
        # Forward pass
        try:
            outputs = model(src_tensor, tgt_tensor)
            print(f"   Output shape: {outputs.shape}")
            print(f"   Output range: [{outputs.min().item():.3f}, {outputs.max().item():.3f}]")
            
            # Get probabilities
            logits = outputs[0, -1]  # Last position
            probs = torch.softmax(logits, dim=-1)
            
            # Top 10 tokens
            top_probs, top_indices = torch.topk(probs, 10)
            print(f"\n   Top 10 predictions:")
            for i, (prob, idx) in enumerate(zip(top_probs, top_indices)):
                token = tokenizer.id_to_piece(idx.item())
                print(f"     {i+1}. {token} (id={idx.item()}, prob={prob.item():.4f})")
            
            # Try generation
            print(f"\n🎯 Testing Generation:")
            max_len = 10
            output_tokens = [2]  # BOS
            
            for step in range(max_len):
                tgt_tensor = torch.tensor([output_tokens], dtype=torch.long).to(device)
                outputs = model(src_tensor, tgt_tensor)
                
                # Get next token
                logits = outputs[0, -1]
                next_token = logits.argmax().item()
                
                print(f"     Step {step+1}: token={next_token}, piece='{tokenizer.id_to_piece(next_token)}'")
                
                if next_token == 3:  # EOS
                    print(f"     → EOS token reached")
                    break
                
                output_tokens.append(next_token)
            
            # Decode result
            if len(output_tokens) > 1:
                result = tokenizer.decode_ids(output_tokens[1:])
                print(f"   Final result: '{result}'")
            else:
                print(f"   No tokens generated")
                
        except Exception as e:
            print(f"   ❌ Error in forward pass: {e}")
            import traceback
            traceback.print_exc()


def test_simple_generation():
    """Test very simple generation."""
    
    print("\n🎲 Simple Generation Test")
    print("-" * 30)
    
    # Load everything
    tokenizer_path = "nunavut_mt/models/tokenizer/tokenizer_v16000.model"
    tokenizer = spm.SentencePieceProcessor()
    tokenizer.load(tokenizer_path)
    
    model_path = "nunavut_mt/models/improved_gpu/improved_translation_model.pt"
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    from train_efficient_gpu import EfficientTranslator
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    vocab_size = checkpoint['vocab_size']
    config = checkpoint['model_config']
    
    model = EfficientTranslator(
        vocab_size=vocab_size,
        d_model=config['d_model'],
        nhead=config['nhead'],
        num_layers=config['num_layers'],
        max_seq_len=config['max_seq_len']
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    with torch.no_grad():
        # Very simple test
        src = torch.tensor([[4, 100, 200]], dtype=torch.long).to(device)  # Simple tokens
        tgt = torch.tensor([[2]], dtype=torch.long).to(device)  # Just BOS
        
        print(f"Testing with src={src.tolist()}, tgt={tgt.tolist()}")
        
        try:
            outputs = model(src, tgt)
            print(f"✅ Model forward pass successful!")
            print(f"   Output shape: {outputs.shape}")
            
            # Get next token
            next_token = outputs[0, -1].argmax().item()
            print(f"   Next token: {next_token}")
            print(f"   Token piece: '{tokenizer.id_to_piece(next_token)}'")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    debug_model()
    test_simple_generation()
