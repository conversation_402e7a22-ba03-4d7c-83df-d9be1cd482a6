#!/usr/bin/env python3
"""
Comprehensive Evaluation for Stage 2 Model
Implements BLEU, chrF++, and semantic similarity metrics.
"""

import torch
import torch.nn.functional as F
import sentencepiece as spm
import json
import time
from pathlib import Path
from typing import List, Dict, Tuple
import logging
import re
from collections import Counter
import math

from stage2_model import Stage2Transformer, create_stage2_model

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BLEUScore:
    """BLEU score implementation."""
    
    def __init__(self, max_n: int = 4):
        self.max_n = max_n
    
    def _get_ngrams(self, tokens: List[str], n: int) -> Counter:
        """Get n-grams from tokens."""
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngrams.append(' '.join(tokens[i:i+n]))
        return Counter(ngrams)
    
    def _brevity_penalty(self, candidate_len: int, reference_len: int) -> float:
        """Calculate brevity penalty."""
        if candidate_len > reference_len:
            return 1.0
        elif candidate_len == 0:
            return 0.0
        else:
            return math.exp(1 - reference_len / candidate_len)
    
    def calculate(self, candidate: str, reference: str) -> float:
        """Calculate BLEU score for single sentence pair."""
        # Tokenize
        candidate_tokens = candidate.strip().split()
        reference_tokens = reference.strip().split()
        
        if len(candidate_tokens) == 0:
            return 0.0
        
        # Calculate precision for each n-gram order
        precisions = []
        for n in range(1, self.max_n + 1):
            candidate_ngrams = self._get_ngrams(candidate_tokens, n)
            reference_ngrams = self._get_ngrams(reference_tokens, n)
            
            if len(candidate_ngrams) == 0:
                precisions.append(0.0)
                continue
            
            # Count matches
            matches = 0
            for ngram, count in candidate_ngrams.items():
                matches += min(count, reference_ngrams.get(ngram, 0))
            
            precision = matches / len(candidate_ngrams)
            precisions.append(precision)
        
        # Geometric mean of precisions
        if any(p == 0 for p in precisions):
            return 0.0
        
        geometric_mean = math.exp(sum(math.log(p) for p in precisions) / len(precisions))
        
        # Brevity penalty
        bp = self._brevity_penalty(len(candidate_tokens), len(reference_tokens))
        
        return bp * geometric_mean

class ChrFScore:
    """chrF++ score implementation."""
    
    def __init__(self, beta: float = 2.0, remove_whitespace: bool = True):
        self.beta = beta
        self.remove_whitespace = remove_whitespace
    
    def _get_char_ngrams(self, text: str, n: int) -> Counter:
        """Get character n-grams."""
        if self.remove_whitespace:
            text = text.replace(' ', '')
        
        ngrams = []
        for i in range(len(text) - n + 1):
            ngrams.append(text[i:i+n])
        return Counter(ngrams)
    
    def _get_word_ngrams(self, text: str, n: int) -> Counter:
        """Get word n-grams."""
        words = text.strip().split()
        ngrams = []
        for i in range(len(words) - n + 1):
            ngrams.append(' '.join(words[i:i+n]))
        return Counter(ngrams)
    
    def calculate(self, candidate: str, reference: str) -> float:
        """Calculate chrF++ score."""
        if not candidate.strip() or not reference.strip():
            return 0.0
        
        # Character n-grams (1-6)
        char_precision_sum = 0
        char_recall_sum = 0
        char_count = 0
        
        for n in range(1, 7):
            cand_ngrams = self._get_char_ngrams(candidate, n)
            ref_ngrams = self._get_char_ngrams(reference, n)
            
            if len(cand_ngrams) == 0 or len(ref_ngrams) == 0:
                continue
            
            # Calculate matches
            matches = sum(min(cand_ngrams[ng], ref_ngrams[ng]) 
                         for ng in cand_ngrams if ng in ref_ngrams)
            
            precision = matches / sum(cand_ngrams.values()) if sum(cand_ngrams.values()) > 0 else 0
            recall = matches / sum(ref_ngrams.values()) if sum(ref_ngrams.values()) > 0 else 0
            
            char_precision_sum += precision
            char_recall_sum += recall
            char_count += 1
        
        # Word n-grams (1-2)
        word_precision_sum = 0
        word_recall_sum = 0
        word_count = 0
        
        for n in range(1, 3):
            cand_ngrams = self._get_word_ngrams(candidate, n)
            ref_ngrams = self._get_word_ngrams(reference, n)
            
            if len(cand_ngrams) == 0 or len(ref_ngrams) == 0:
                continue
            
            matches = sum(min(cand_ngrams[ng], ref_ngrams[ng]) 
                         for ng in cand_ngrams if ng in ref_ngrams)
            
            precision = matches / sum(cand_ngrams.values()) if sum(cand_ngrams.values()) > 0 else 0
            recall = matches / sum(ref_ngrams.values()) if sum(ref_ngrams.values()) > 0 else 0
            
            word_precision_sum += precision
            word_recall_sum += recall
            word_count += 1
        
        # Combine character and word scores
        if char_count == 0 and word_count == 0:
            return 0.0
        
        total_precision = (char_precision_sum + word_precision_sum) / (char_count + word_count)
        total_recall = (char_recall_sum + word_recall_sum) / (char_count + word_count)
        
        if total_precision + total_recall == 0:
            return 0.0
        
        # F-score with beta
        f_score = (1 + self.beta**2) * total_precision * total_recall / \
                  (self.beta**2 * total_precision + total_recall)
        
        return f_score

class Stage2Evaluator:
    """Comprehensive evaluator for Stage 2 model."""
    
    def __init__(self, model: Stage2Transformer, tokenizer_path: str, device: torch.device):
        self.model = model
        self.device = device
        
        # Load tokenizer
        self.tokenizer = spm.SentencePieceProcessor()
        self.tokenizer.load(tokenizer_path)
        
        # Metrics
        self.bleu_scorer = BLEUScore()
        self.chrf_scorer = ChrFScore()
        
        # Special tokens
        self.bos_id = self.tokenizer.bos_id()
        self.eos_id = self.tokenizer.eos_id()
        self.pad_id = self.tokenizer.pad_id()
    
    def translate_sentence(self, source_text: str, max_length: int = 128, 
                          beam_size: int = 4, temperature: float = 0.8) -> str:
        """Translate a single sentence using beam search."""
        self.model.eval()
        
        with torch.no_grad():
            # Tokenize source
            src_tokens = [self.bos_id] + self.tokenizer.encode(source_text) + [self.eos_id]
            src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
            
            # Encode source
            src_mask = (src_tensor != self.pad_id).unsqueeze(1).unsqueeze(2)
            memory = self.model.encode(src_tensor, src_mask)
            
            # Initialize beam search
            beam_sequences = [[self.bos_id]]
            beam_scores = [0.0]
            
            for step in range(max_length):
                candidates = []
                
                for seq_idx, sequence in enumerate(beam_sequences):
                    if sequence[-1] == self.eos_id:
                        candidates.append((sequence, beam_scores[seq_idx]))
                        continue
                    
                    # Prepare target tensor
                    tgt_tensor = torch.tensor(sequence).unsqueeze(0).to(self.device)
                    tgt_mask = self.model.generate_square_subsequent_mask(len(sequence)).unsqueeze(0).to(self.device)
                    
                    # Decode
                    output = self.model.decode(tgt_tensor, memory, tgt_mask)
                    logits = self.model.output_proj(self.model.output_norm(output))
                    
                    # Get probabilities for next token
                    next_token_logits = logits[0, -1, :] / temperature
                    probs = F.softmax(next_token_logits, dim=-1)
                    
                    # Get top-k candidates
                    top_probs, top_indices = torch.topk(probs, beam_size)
                    
                    for prob, token_id in zip(top_probs, top_indices):
                        new_sequence = sequence + [token_id.item()]
                        new_score = beam_scores[seq_idx] + torch.log(prob).item()
                        candidates.append((new_sequence, new_score))
                
                # Select top beam_size candidates
                candidates.sort(key=lambda x: x[1], reverse=True)
                beam_sequences = [seq for seq, score in candidates[:beam_size]]
                beam_scores = [score for seq, score in candidates[:beam_size]]
                
                # Check if all beams ended
                if all(seq[-1] == self.eos_id for seq in beam_sequences):
                    break
            
            # Select best sequence
            best_sequence = beam_sequences[0]
            
            # Remove special tokens and decode
            if self.eos_id in best_sequence:
                best_sequence = best_sequence[:best_sequence.index(self.eos_id)]
            if best_sequence[0] == self.bos_id:
                best_sequence = best_sequence[1:]
            
            return self.tokenizer.decode(best_sequence)
    
    def evaluate_dataset(self, en_file: str, iu_file: str, max_samples: int = 1000) -> Dict[str, float]:
        """Evaluate model on a dataset."""
        logger.info(f"Evaluating on {en_file} and {iu_file}")
        
        # Load test data
        with open(en_file, 'r', encoding='utf-8') as f:
            en_sentences = [line.strip() for line in f if line.strip()]
        
        with open(iu_file, 'r', encoding='utf-8') as f:
            iu_sentences = [line.strip() for line in f if line.strip()]
        
        # Limit samples for faster evaluation
        if len(en_sentences) > max_samples:
            en_sentences = en_sentences[:max_samples]
            iu_sentences = iu_sentences[:max_samples]
        
        logger.info(f"Evaluating on {len(en_sentences)} sentence pairs")
        
        # Evaluate
        bleu_scores = []
        chrf_scores = []
        translation_times = []
        
        for i, (en_text, iu_ref) in enumerate(zip(en_sentences, iu_sentences)):
            if i % 100 == 0:
                logger.info(f"Processed {i}/{len(en_sentences)} sentences")
            
            # Translate
            start_time = time.time()
            iu_pred = self.translate_sentence(en_text)
            translation_time = time.time() - start_time
            translation_times.append(translation_time)
            
            # Calculate metrics
            bleu_score = self.bleu_scorer.calculate(iu_pred, iu_ref)
            chrf_score = self.chrf_scorer.calculate(iu_pred, iu_ref)
            
            bleu_scores.append(bleu_score)
            chrf_scores.append(chrf_score)
            
            # Log some examples
            if i < 5:
                logger.info(f"Example {i+1}:")
                logger.info(f"  EN: {en_text}")
                logger.info(f"  IU_REF: {iu_ref}")
                logger.info(f"  IU_PRED: {iu_pred}")
                logger.info(f"  BLEU: {bleu_score:.4f}, chrF++: {chrf_score:.4f}")
        
        # Calculate averages
        avg_bleu = sum(bleu_scores) / len(bleu_scores)
        avg_chrf = sum(chrf_scores) / len(chrf_scores)
        avg_time = sum(translation_times) / len(translation_times)
        
        results = {
            'bleu_score': avg_bleu,
            'chrf_score': avg_chrf,
            'avg_translation_time': avg_time,
            'total_sentences': len(en_sentences),
            'sentences_per_second': 1.0 / avg_time if avg_time > 0 else 0
        }
        
        logger.info(f"Evaluation Results:")
        logger.info(f"  BLEU Score: {avg_bleu:.4f}")
        logger.info(f"  chrF++ Score: {avg_chrf:.4f}")
        logger.info(f"  Avg Translation Time: {avg_time:.4f}s")
        logger.info(f"  Sentences/Second: {results['sentences_per_second']:.2f}")
        
        return results

def load_stage2_model(checkpoint_path: str, device: torch.device) -> Stage2Transformer:
    """Load Stage 2 model from checkpoint."""
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    model = create_stage2_model(
        vocab_size=checkpoint['model_config']['vocab_size'],
        device=device
    )
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    logger.info(f"Loaded model from {checkpoint_path}")
    return model

def main():
    """Main evaluation function."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Paths
    checkpoint_path = "models/stage2/stage2_latest.pt"
    tokenizer_path = "models/tokenizer/tokenizer_v16000.model"
    test_en = "../split/test.en"
    test_iu = "../split/test.iu"
    
    # Load model
    try:
        model = load_stage2_model(checkpoint_path, device)
    except FileNotFoundError:
        logger.error(f"Checkpoint not found: {checkpoint_path}")
        logger.info("Please train the Stage 2 model first using train_stage2.py")
        return
    
    # Create evaluator
    evaluator = Stage2Evaluator(model, tokenizer_path, device)
    
    # Evaluate
    results = evaluator.evaluate_dataset(test_en, test_iu, max_samples=500)
    
    # Save results
    results_path = Path("models/stage2/evaluation_results.json")
    results_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {results_path}")
    
    # Check Stage 2 targets
    target_bleu = 12.0
    if results['bleu_score'] >= target_bleu:
        logger.info(f"✅ Stage 2 BLEU target achieved: {results['bleu_score']:.4f} >= {target_bleu}")
    else:
        logger.info(f"❌ Stage 2 BLEU target not met: {results['bleu_score']:.4f} < {target_bleu}")

if __name__ == "__main__":
    main()
